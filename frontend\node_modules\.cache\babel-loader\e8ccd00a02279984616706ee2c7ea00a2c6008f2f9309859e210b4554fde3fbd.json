{"ast": null, "code": "const API_BASE_URL = 'http://127.0.0.1:3001'; //如果时本地访问服务端，使用这个给地址。\n//'http://zbh.fjsbqxh.com'; // 部署到服务器上时，使用域名而不是IP地址\n//'http://*************' ; //这个时部署在外网服务器上的服务端地址。\n//process.env.REACT_APP_API_URL; \n// 'http://127.0.0.1:3001';  //如果时本地访问服务端，使用这个给地址。\n\nexport const API_ENDPOINTS = {\n  LOGIN: `${API_BASE_URL}/api/login`,\n  REFRESH_TOKEN: `${API_BASE_URL}/api/refresh-token`,\n  ADD_USER: `${API_BASE_URL}/api/adduser`,\n  UPDATE_USER: `${API_BASE_URL}/api/update`,\n  GET_USER_INFO: `${API_BASE_URL}/api/get-user-info`,\n  GET_USERS_BY_PREFIX: `${API_BASE_URL}/api/by-shortname-prefix`,\n  GET_HIERARCHY_SOFTWARE_AUTH: `${API_BASE_URL}/api/get-hierarchy-software-auth`,\n  GET_USER_AUTH_DATA: `${API_BASE_URL}/api/user_auth-data`,\n  GET_USER_BASE_DATA: `${API_BASE_URL}/api/user_base-data`,\n  GET_USER_SELF_CHECK_DATA: `${API_BASE_URL}/api/user_self-check-data`,\n  UPLOAD_AUTH_SOFTWARE: `${API_BASE_URL}/api/upload_auth-software`,\n  UPLOAD_SELF_CHECK: `${API_BASE_URL}/api/upload_self-check`,\n  GET_SELF_CHECK_DATA: `${API_BASE_URL}/api/get-self-check-data`,\n  GET_AUTH_STATISTICS: `${API_BASE_URL}/api/statistics_auth`,\n  GET_CHECK_STATISTICS: `${API_BASE_URL}/api/statistics_check`,\n  GET_COMPUTER_STATISTICS: `${API_BASE_URL}/api/statistics_computer`,\n  GET_SINGLE_DATA: `${API_BASE_URL}/api/auth-software`,\n  DELETE_SELF_CHECK: `${API_BASE_URL}/api/delete_self_check`,\n  UPDATE_SELF_CHECK: `${API_BASE_URL}/api/update_self_check`,\n  DELETE_MY_SELECT_AUTH: `${API_BASE_URL}/api/user/auth-data/delete`,\n  UPDATE_MY_SELECT_AUTH: `${API_BASE_URL}/api/update_auth`,\n  ADD_COMPUTERINFO: `${API_BASE_URL}/api/add_computerinfo`,\n  GET_COMPUTERINFO: `${API_BASE_URL}/api/get_computerinfo`,\n  UPDATE_COMPUTERINFO: `${API_BASE_URL}/api/update_computerinfo`,\n  GET_SOFTWARE_AUTH_RATE: `${API_BASE_URL}/api/statistics_software_auth_rate`,\n  ADMIN_GET_ALL_USERS: `${API_BASE_URL}/api/admin/all-users`,\n  ADMIN_DELETE_USER: `${API_BASE_URL}/api/admin/delete-user`,\n  ADMIN_GET_ALL_SUB_USER: `${API_BASE_URL}/api/admin/delete-user`\n};\nexport default API_BASE_URL;", "map": {"version": 3, "names": ["API_BASE_URL", "API_ENDPOINTS", "LOGIN", "REFRESH_TOKEN", "ADD_USER", "UPDATE_USER", "GET_USER_INFO", "GET_USERS_BY_PREFIX", "GET_HIERARCHY_SOFTWARE_AUTH", "GET_USER_AUTH_DATA", "GET_USER_BASE_DATA", "GET_USER_SELF_CHECK_DATA", "UPLOAD_AUTH_SOFTWARE", "UPLOAD_SELF_CHECK", "GET_SELF_CHECK_DATA", "GET_AUTH_STATISTICS", "GET_CHECK_STATISTICS", "GET_COMPUTER_STATISTICS", "GET_SINGLE_DATA", "DELETE_SELF_CHECK", "UPDATE_SELF_CHECK", "DELETE_MY_SELECT_AUTH", "UPDATE_MY_SELECT_AUTH", "ADD_COMPUTERINFO", "GET_COMPUTERINFO", "UPDATE_COMPUTERINFO", "GET_SOFTWARE_AUTH_RATE", "ADMIN_GET_ALL_USERS", "ADMIN_DELETE_USER", "ADMIN_GET_ALL_SUB_USER"], "sources": ["F:/正版化平台项目/zbh-0801开发版 - 副本/frontend/src/config.js"], "sourcesContent": ["const API_BASE_URL = 'http://127.0.0.1:3001';  //如果时本地访问服务端，使用这个给地址。\n//'http://zbh.fjsbqxh.com'; // 部署到服务器上时，使用域名而不是IP地址\n//'http://*************' ; //这个时部署在外网服务器上的服务端地址。\n//process.env.REACT_APP_API_URL; \n// 'http://127.0.0.1:3001';  //如果时本地访问服务端，使用这个给地址。\n\nexport const API_ENDPOINTS = {\n    LOGIN: `${API_BASE_URL}/api/login`,\n    REFRESH_TOKEN: `${API_BASE_URL}/api/refresh-token`,\n    ADD_USER: `${API_BASE_URL}/api/adduser`,\n    UPDATE_USER: `${API_BASE_URL}/api/update`,\n    GET_USER_INFO: `${API_BASE_URL}/api/get-user-info`,\n    GET_USERS_BY_PREFIX: `${API_BASE_URL}/api/by-shortname-prefix`,\n    GET_HIERARCHY_SOFTWARE_AUTH: `${API_BASE_URL}/api/get-hierarchy-software-auth`,\n    GET_USER_AUTH_DATA: `${API_BASE_URL}/api/user_auth-data`,\n    GET_USER_BASE_DATA: `${API_BASE_URL}/api/user_base-data`,\n    GET_USER_SELF_CHECK_DATA: `${API_BASE_URL}/api/user_self-check-data`,\n    UPLOAD_AUTH_SOFTWARE: `${API_BASE_URL}/api/upload_auth-software`,\n    UPLOAD_SELF_CHECK: `${API_BASE_URL}/api/upload_self-check`,\n    GET_SELF_CHECK_DATA: `${API_BASE_URL}/api/get-self-check-data`,\n    GET_AUTH_STATISTICS: `${API_BASE_URL}/api/statistics_auth`,\n    GET_CHECK_STATISTICS: `${API_BASE_URL}/api/statistics_check`,\n    GET_COMPUTER_STATISTICS: `${API_BASE_URL}/api/statistics_computer`,\n    GET_SINGLE_DATA: `${API_BASE_URL}/api/auth-software`,\n    DELETE_SELF_CHECK: `${API_BASE_URL}/api/delete_self_check`,\n    UPDATE_SELF_CHECK: `${API_BASE_URL}/api/update_self_check`,\n    DELETE_MY_SELECT_AUTH: `${API_BASE_URL}/api/user/auth-data/delete`,\n    UPDATE_MY_SELECT_AUTH: `${API_BASE_URL}/api/update_auth`,\n    ADD_COMPUTERINFO:`${API_BASE_URL}/api/add_computerinfo`,\n    GET_COMPUTERINFO:`${API_BASE_URL}/api/get_computerinfo`,\n    UPDATE_COMPUTERINFO:`${API_BASE_URL}/api/update_computerinfo`,\n    GET_SOFTWARE_AUTH_RATE:`${API_BASE_URL}/api/statistics_software_auth_rate`,\n    ADMIN_GET_ALL_USERS: `${API_BASE_URL}/api/admin/all-users`,\n    ADMIN_DELETE_USER: `${API_BASE_URL}/api/admin/delete-user`,\n    ADMIN_GET_ALL_SUB_USER: `${API_BASE_URL}/api/admin/delete-user`,\n    \n};\n\n\nexport default API_BASE_URL; "], "mappings": "AAAA,MAAMA,YAAY,GAAG,uBAAuB,CAAC,CAAE;AAC/C;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,aAAa,GAAG;EACzBC,KAAK,EAAE,GAAGF,YAAY,YAAY;EAClCG,aAAa,EAAE,GAAGH,YAAY,oBAAoB;EAClDI,QAAQ,EAAE,GAAGJ,YAAY,cAAc;EACvCK,WAAW,EAAE,GAAGL,YAAY,aAAa;EACzCM,aAAa,EAAE,GAAGN,YAAY,oBAAoB;EAClDO,mBAAmB,EAAE,GAAGP,YAAY,0BAA0B;EAC9DQ,2BAA2B,EAAE,GAAGR,YAAY,kCAAkC;EAC9ES,kBAAkB,EAAE,GAAGT,YAAY,qBAAqB;EACxDU,kBAAkB,EAAE,GAAGV,YAAY,qBAAqB;EACxDW,wBAAwB,EAAE,GAAGX,YAAY,2BAA2B;EACpEY,oBAAoB,EAAE,GAAGZ,YAAY,2BAA2B;EAChEa,iBAAiB,EAAE,GAAGb,YAAY,wBAAwB;EAC1Dc,mBAAmB,EAAE,GAAGd,YAAY,0BAA0B;EAC9De,mBAAmB,EAAE,GAAGf,YAAY,sBAAsB;EAC1DgB,oBAAoB,EAAE,GAAGhB,YAAY,uBAAuB;EAC5DiB,uBAAuB,EAAE,GAAGjB,YAAY,0BAA0B;EAClEkB,eAAe,EAAE,GAAGlB,YAAY,oBAAoB;EACpDmB,iBAAiB,EAAE,GAAGnB,YAAY,wBAAwB;EAC1DoB,iBAAiB,EAAE,GAAGpB,YAAY,wBAAwB;EAC1DqB,qBAAqB,EAAE,GAAGrB,YAAY,4BAA4B;EAClEsB,qBAAqB,EAAE,GAAGtB,YAAY,kBAAkB;EACxDuB,gBAAgB,EAAC,GAAGvB,YAAY,uBAAuB;EACvDwB,gBAAgB,EAAC,GAAGxB,YAAY,uBAAuB;EACvDyB,mBAAmB,EAAC,GAAGzB,YAAY,0BAA0B;EAC7D0B,sBAAsB,EAAC,GAAG1B,YAAY,oCAAoC;EAC1E2B,mBAAmB,EAAE,GAAG3B,YAAY,sBAAsB;EAC1D4B,iBAAiB,EAAE,GAAG5B,YAAY,wBAAwB;EAC1D6B,sBAAsB,EAAE,GAAG7B,YAAY;AAE3C,CAAC;AAGD,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}