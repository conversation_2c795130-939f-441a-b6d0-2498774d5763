const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');


// You will need to import your database connection or data access methods here
// const db = require('../path/to/your/db');

// POST endpoint to get software authorization data for a specific user
// 添加认证中间件保护此路由
/*
router.post('/api/user_base-data', authMiddleware, async (req, res) => {
  const { username } = req.body;

  if (!username) {
    return res.status(400).json({ message: 'Username is required' });
  }

  let connection;

  try {
    connection = await pool.getConnection();

    // Use prepared statement to prevent SQL injection
    const query = 'SELECT domestic_computer, not_domestic_computer FROM computerinfo WHERE username = ?';
    const [rows] = await connection.execute(query, [username]);

    // The result is in 'rows'
    console.log(`Fetched software authorization data for user ${username}:`, rows);

    res.status(200).json(rows);

  } catch (error) {
    console.error(`Error fetching auth data for user ${username}:`, error);
    res.status(500).json({ message: 'Error fetching software authorization data', error: error.message });
  } finally {
    // Ensure the connection is released even if an error occurs
    if (connection) {
      connection.release();
    }
  }
});
*/
router.post('/api/user_base-data',authMiddleware,async(req,res) => {
    const { username } = req.body;
    if (!username) {
      return res.status(400).json({ message: '用户名称是必需的' });
    }
    let connection;
    try {
      connection = await pool.getConnection();
      
      // 1. 查询电脑总数
      const query = 'SELECT domestic_computer, not_domestic_computer FROM computerinfo WHERE username = ?';
      const [computerRows] = await connection.execute(query, [username]);
  
      let domestic = 0;
      let not_domestic = 0;
      if (computerRows.length > 0 && computerRows[0]) {
          // 确保从正确的列名取值，并处理null/undefined情况
          domestic = Number(computerRows[0].domestic_computer) || 0;
          not_domestic = Number(computerRows[0].not_domestic_computer) || 0;
      }
      const totalComputers = domestic + not_domestic;
      // 2. 查询每个软件类别的授权总数
      let authQuery = `SELECT software_type as name, SUM(auth_number) as value FROM authsoftware WHERE owner = ? GROUP BY software_type`;
      const [authRows] = await connection.execute(authQuery, [username]);
  
      // 3. 计算正版化率
      const auth_rates = authRows.map(row => ({
        name: row.name,
        value: totalComputers > 0 ? ((Number(row.value) / totalComputers) * 100).toFixed(2) : "0.00"
      }));
      
      // 4. 组合所有数据并以单一JSON对象响应
      res.json({
          success: true,
          data: {
              domestic_computer: domestic,
              not_domestic_computer: not_domestic,
              total_computers: totalComputers,
              auth_rates: auth_rates
          }
      });
  
    } catch (err) {
      console.error('统计软件正版化率出错:', err);
      res.status(500).json({ message: '服务器错误' });
    } finally {
      if (connection) connection.release();
    }
  
  });



// Admin route to get all users
router.get('/api/admin/all-users', authMiddleware, async (req, res) => {
  // First, check if the user is an admin
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Forbidden: Admins only' });
  }

  let connection;
  try {
    connection = await pool.getConnection();
    const [users] = await connection.execute('SELECT  username, email, mobile, address, short_name, contact, organization_type FROM users ORDER BY organization_type ASC, username ASC');
    res.json(users);
  } catch (error) {
    console.error('Error fetching all users:', error);
    res.status(500).json({ message: 'Failed to fetch users' });
  } finally {
    if (connection) {
      connection.release();
    }
  }
});



// Admin route to delete a user
router.delete('/api/admin/delete-user', authMiddleware, async (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Forbidden: Admins only' });
  }

  const { username } = req.body;
  let connection;

  try {
    connection = await pool.getConnection();
    await connection.execute('DELETE FROM users WHERE username = ?', [username]);
    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Failed to delete user' });
  } finally {
    if (connection) {
      connection.release();
    }
  }
});

module.exports = router;

