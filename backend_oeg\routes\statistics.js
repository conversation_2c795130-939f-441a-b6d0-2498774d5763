const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');

// Helper function to recursively fetch usernames based on short_name hierarchy
// 添加认证中间件保护此路由
async function fetchUsernamesByShortNameHierarchy(startShortName, connection) {
  const visitedShortNames = new Set();
  const allUsernames = new Set();

  // 添加起始用户的 username
  const [startUserRows] = await connection.execute(
    'SELECT username FROM users WHERE short_name = ?',
    [startShortName]
  );
  if (startUserRows.length > 0) {
    allUsernames.add(startUserRows[0].username);
    visitedShortNames.add(startShortName); // Mark start node as visited
  }

  // 递归查找下一级
  async function processNextLevel(currentShortName) {
    const parts = currentShortName.split('-');
    // Ensure we have at least X-Y-Z structure
    if (parts.length >= 3) {
      const x = parseInt(parts[0], 10);
      const z = parts[2];

      // Ensure X is a valid number
      if (!isNaN(x)) {
        // Calculate the next level prefix (X+1)-Z-
        const nextPrefix = `${x + 1}-${z}-`;

        // Query for users with short_name starting with the next prefix
        const [users] = await connection.execute(
          'SELECT username, short_name FROM users WHERE short_name LIKE ?',
          [nextPrefix + '%']
        );

        // Recursively process found users
        for (const user of users) {
          if (!visitedShortNames.has(user.short_name)) {
            visitedShortNames.add(user.short_name);
            allUsernames.add(user.username);
            await processNextLevel(user.short_name); // Recursive call
          }
        }
      }
    }
  }

  // Start the recursive process from the initial user's short_name
  await processNextLevel(startShortName);

  return Array.from(allUsernames);
}

// 统计软件授权情况
// POST /api/statistics/auth
// body: { year: '2023', category: '软件名称' | '软件类型' | '授权时限' }
router.post('/api/statistics_auth', authMiddleware, async (req, res) => {
  const { year, category, userShortName } = req.body;
  if (!userShortName) {
    return res.status(400).json({ message: '用户简称是必需的' });
  }
  
  let groupField = '';
  if (category === '软件名称') groupField = 'software_name';
  else if (category === '软件类型') groupField = 'software_type';
  else if (category === '授权时限') groupField = 'auth_timelimit';
  else return res.status(400).json({ message: '无效的统计类别' });

  let connection;

  try {
    connection = await pool.getConnection();

    // 1. Fetch relevant usernames based on the hierarchy
    const [startUserRows] = await connection.execute(
      'SELECT username FROM users WHERE short_name = ?',
      [userShortName]
    );

    let usernamesArr = [];
    if (startUserRows.length > 0) {
       usernamesArr.push(startUserRows[0].username); // Add the start user's username
       // Fetch hierarchy users excluding the start user (handled by visitedSet inside)
       const hierarchyUsernames = await fetchUsernamesByShortNameHierarchy(userShortName, connection);
       // Combine and ensure uniqueness (Set handles uniqueness)
       usernamesArr = Array.from(new Set([...usernamesArr, ...hierarchyUsernames]));
    }

    // 移除敏感信息日志记录

    if (usernamesArr.length === 0) {
      if (connection) connection.release();
      return res.status(200).json([]);
    }

    // 2. Construct the SQL query with the IN clause
    const placeholders = usernamesArr.map(() => '?').join(',');
    let query = `SELECT ${groupField} as name, SUM(auth_number) as value\n       FROM authsoftware\n       WHERE owner IN (${placeholders})`;

    let params = [...usernamesArr];

    if (year !== '全部') {
      if (year === '2018及以前') {
        query += `\n       AND YEAR(buytime) <= 2018`;
      } else {
        query += `\n       AND YEAR(buytime) = ?`;
        params.push(year);
      }
    }

    query += `\n       GROUP BY ${groupField}\n       ORDER BY value DESC`;
    const [rows] = await connection.execute(query, params);

    res.json(rows);
  } catch (err) {
    console.error('统计软件授权情况出错:', err);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    if (connection) connection.release();
  }
});

// 统计软件自查情况
// POST /api/statistics/check
// body: { year: '2023', category: '软件名称' | '软件类型' }
router.post('/api/statistics_check', authMiddleware, async (req, res) => {
  const { year, category, userShortName } = req.body;
  if (!userShortName) {
    return res.status(400).json({ message: '用户简称是必需的' });
  }

  let groupField = '';
  let groupByYear = false;
  if (category === '软件名称') groupField = 'software_name';
  else if (category === '软件类型') groupField = 'software_type';
  else if (category === '自查年份') {
    groupField = 'YEAR(check_time)';
    groupByYear = true;
  }
  else return res.status(400).json({ message: '无效的统计类别' });

  let connection;

  try {
    connection = await pool.getConnection();

    // 1. Fetch relevant usernames based on the hierarchy
    const [startUserRows] = await connection.execute(
      'SELECT username FROM users WHERE short_name = ?',
      [userShortName]
    );
    
        
    const usernamesArr = await fetchUsernamesByShortNameHierarchy(userShortName, connection);

    // 移除敏感信息日志记录

    if (usernamesArr.length === 0) {
      if (connection) connection.release();
      return res.status(200).json([]);
    }
    
    // 2. Construct the SQL query with the IN clause
    const placeholders = usernamesArr.map(() => '?').join(',');
    let query;
    if (groupByYear) {
      query = `SELECT YEAR(check_time) as name, SUM(install_num) as value
               FROM checksoftware
               WHERE checkedman IN (${placeholders})`;
    } else {
      query = `SELECT ${groupField} as name, SUM(install_num) as value
               FROM checksoftware
               WHERE checkedman IN (${placeholders})`;
    }

    let params = [...usernamesArr];

    if (year !== '全部') {
      if (year === '2018及以前') {
        query += `\n       AND YEAR(check_time) <= 2018`;
      } else {
        query += `\n       AND YEAR(check_time) = ?`;
        params.push(year);
      }
    }

    if (groupByYear) {
      query += ` GROUP BY YEAR(check_time) ORDER BY value DESC`;
    } else {
      query += ` GROUP BY ${groupField} ORDER BY value DESC`;
    }
    const [rows] = await connection.execute(query, params);
       
    res.json(rows);
  } catch (err) {
    console.error('统计软件自查情况出错:', err);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    if (connection) connection.release();
  }
});


//统计电脑数量
router.post('/api/statistics_computer',authMiddleware,async (req,res) =>{
  const { userShortName } = req.body;
  if (!userShortName) {
    return res.status(400).json({ message: '用户简称是必需的' });
  }
  let connection;
  try {
    connection = await pool.getConnection();
    const usernamesArr = await fetchUsernamesByShortNameHierarchy(userShortName, connection);
    if (usernamesArr.length === 0) {
      connection.release();
      return res.status(200).json({ success: true, data: { domestic: 0, foreign: 0 } });
    }
    const placeholders = usernamesArr.map(() => '?').join(',');
    const query = `SELECT SUM(domestic_computer) as domestic, SUM(not_domestic_computer) as not_domestic FROM computerinfo WHERE username IN (${placeholders})`;
    const [rows] = await connection.execute(query, usernamesArr);
    res.json({success: true, data: rows[0]}); //success: true, data: rows[0]
  } catch (err) {
    console.error('统计电脑数据出错:', err);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    if (connection) connection.release();
  }
});

// 统计软件正版化率
router.post('/api/statistics_software_auth_rate', authMiddleware, async (req, res) => {
  const { year, userShortName } = req.body;
  if (!userShortName) {
    return res.status(400).json({ message: '用户简称是必需的' });
  }
  let connection;
  try {
    connection = await pool.getConnection();
    // 1. 获取所有相关用户
    const usernamesArr = await fetchUsernamesByShortNameHierarchy(userShortName, connection);
    if (usernamesArr.length === 0) {
      connection.release();
      return res.status(200).json([]);
    }
    const placeholders = usernamesArr.map(() => '?').join(',');

    // 2. 查询电脑总数
    const [computerRows] = await connection.execute(
      `SELECT SUM(domestic_computer) as domestic, SUM(not_domestic_computer) as not_domestic FROM computerinfo WHERE username IN (${placeholders})`,
      usernamesArr
    );
    // 强制类型转换，防止为字符串
    const domestic = Number(computerRows[0].domestic) || 0;
    const not_domestic = Number(computerRows[0].not_domestic) || 0;
    const totalComputers = domestic + not_domestic;
    console.log("totalComputers is :",totalComputers);
    // 3. 查询每个软件类别的授权总数
    let query = `SELECT software_type as name, SUM(auth_number) as value FROM authsoftware WHERE owner IN (${placeholders})`;
    let params = [...usernamesArr];
    if (year && year !== '全部') {
      if (year === '2018及以前') {
        query += ` AND YEAR(buytime) <= 2018`;
      } else {
        query += ` AND YEAR(buytime) = ?`;
        params.push(year);
      }
    }
    query += ` GROUP BY software_type`;

    const [rows] = await connection.execute(query, params);

    // 4. 计算正版化率
    const result = rows.map(row => ({
      name: row.name,
      value: totalComputers > 0 ? (((row.value) / totalComputers) * 100).toFixed(2) : 0
    }));

    res.json(result);
    console.log("res is :",res);
  } catch (err) {
    console.error('统计软件正版化率出错:', err);
    res.status(500).json({ message: '服务器错误' });
  } finally {
    if (connection) connection.release();
  }
});


//统计单一用户的基本数据




module.exports = router;
