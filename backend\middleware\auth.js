const jwt = require('jsonwebtoken');

/**
 * JWT认证中间件
 * 验证请求头中的Authorization令牌
 */
const authMiddleware = (req, res, next) => {
  // 从请求头获取令牌
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN格式
  
  if (!token) {
    return res.status(401).json({ 
      status: 'error', 
      message: '未提供认证令牌' 
    });
  }

  try {
    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 将解码后的用户信息添加到请求对象
    req.user = decoded;
    
    next();
  } catch (error) {
    console.error('令牌验证失败:', error.message);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        status: 'error', 
        message: '令牌已过期',
        expired: true
      });
    }
    
    return res.status(403).json({ 
      status: 'error', 
      message: '无效的认证令牌' 
    });
  }
};

module.exports = authMiddleware;