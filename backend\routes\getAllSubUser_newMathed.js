const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');
const HierarchyManager = require('../utils/hierarchyManager');

// 通过short_name获取所有子用户的short_name
router.post('/api/get_all_subuser_sn', authMiddleware, async (req, res) => {
    const { short_name, max_depth } = req.body;
  
    // 基本验证
    if (!short_name) {
      return res.status(400).json({ 
          success: false,
          message: 'short_name is required' 
      });
    }
    
    try {
        // 使用层级管理器获取所有后代
        const descendants = await HierarchyManager.getAllDescendants(short_name, max_depth);
        
        // 提取short_name列表
        const shortNames = descendants.map(user => user.short_name);
        
        res.status(200).json({
            success: true,
            data: shortNames,
            count: shortNames.length,
            max_depth: max_depth || 'unlimited'
        });

    } catch (error) {
        console.error('Error in getAllSubUser:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error',
            error: error.message 
        });
    }
});

// 通过username获取所有子用户的username
router.post('/api/get_all_subuser_username', authMiddleware, async (req, res) => {
    const { username, max_depth } = req.body;
  
    if (!username) {
      return res.status(400).json({ 
          success: false,
          message: 'username is required' 
      });
    }
    
    try {
        const descendants = await HierarchyManager.getAllDescendants(username, max_depth);
        const usernames = descendants.map(user => user.username);
        
        res.status(200).json({
            success: true,
            data: usernames,
            count: usernames.length,
            max_depth: max_depth || 'unlimited'
        });

    } catch (error) {
        console.error('Error in getAllSubUserUsername:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error',
            error: error.message 
        });
    }
});

// 通过short_name获取所有子用户的username
router.post('/api/get_all_subuser_username_by_sn', authMiddleware, async (req, res) => {
    const { short_name, max_depth } = req.body;
  
    if (!short_name) {
      return res.status(400).json({ 
          success: false,
          message: 'short_name is required' 
      });
    }
    
    try {
        const descendants = await HierarchyManager.getAllDescendants(short_name, max_depth);
        const usernames = descendants.map(user => user.username);
        
        res.status(200).json({
            success: true,
            data: usernames,
            count: usernames.length,
            max_depth: max_depth || 'unlimited'
        });

    } catch (error) {
        console.error('Error in getAllSubUserUsernameBySn:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error',
            error: error.message 
        });
    }
});

// 通过username获取所有子用户的short_name
router.post('/api/get_all_subuser_sn_by_username', authMiddleware, async (req, res) => {
    const { username, max_depth } = req.body;
  
    if (!username) {
      return res.status(400).json({ 
          success: false,
          message: 'username is required' 
      });
    }
    
    try {
        const descendants = await HierarchyManager.getAllDescendants(username, max_depth);
        const shortNames = descendants.map(user => user.short_name);
        
        res.status(200).json({
            success: true,
            data: shortNames,
            count: shortNames.length,
            max_depth: max_depth || 'unlimited'
        });

    } catch (error) {
        console.error('Error in getAllSubUserSnByUsername:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error',
            error: error.message 
        });
    }
});

// 获取直接子用户
router.post('/api/get_direct_children', authMiddleware, async (req, res) => {
    const { user_identifier } = req.body; // 可以是ID、username或short_name
  
    if (!user_identifier) {
      return res.status(400).json({ 
          success: false,
          message: 'user_identifier is required' 
      });
    }
    
    try {
        const children = await HierarchyManager.getDirectChildren(user_identifier);
        
        res.status(200).json({
            success: true,
            data: children,
            count: children.length
        });

    } catch (error) {
        console.error('Error in getDirectChildren:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error',
            error: error.message 
        });
    }
});

// 设置用户父级关系
router.post('/api/set_user_parent', authMiddleware, async (req, res) => {
    const { user_identifier, parent_identifier } = req.body;
  
    if (!user_identifier || !parent_identifier) {
      return res.status(400).json({ 
          success: false,
          message: 'Both user_identifier and parent_identifier are required' 
      });
    }
    
    try {
        const result = await HierarchyManager.setParent(user_identifier, parent_identifier);
        
        res.status(200).json({
            success: true,
            message: 'Parent relationship set successfully',
            data: result
        });

    } catch (error) {
        console.error('Error in setUserParent:', error);
        res.status(500).json({ 
            success: false, 
            message: error.message || 'Internal server error'
        });
    }
});

module.exports = router;