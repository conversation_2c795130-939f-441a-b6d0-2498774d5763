const API_BASE_URL = 'http://127.0.0.1:3001';  //如果时本地访问服务端，使用这个给地址。
//'http://zbh.fjsbqxh.com'; // 部署到服务器上时，使用域名而不是IP地址
//'http://*************' ; //这个时部署在外网服务器上的服务端地址。
//process.env.REACT_APP_API_URL; 
// 'http://127.0.0.1:3001';  //如果时本地访问服务端，使用这个给地址。

export const API_ENDPOINTS = {
    LOGIN: `${API_BASE_URL}/api/login`,
    REFRESH_TOKEN: `${API_BASE_URL}/api/refresh-token`,
    ADD_USER: `${API_BASE_URL}/api/adduser`,
    UPDATE_USER: `${API_BASE_URL}/api/update`,
    GET_USER_INFO: `${API_BASE_URL}/api/get-user-info`,
    GET_USERS_BY_PREFIX: `${API_BASE_URL}/api/by-shortname-prefix`,
    GET_HIERARCHY_SOFTWARE_AUTH: `${API_BASE_URL}/api/get-hierarchy-software-auth`,
    GET_USER_AUTH_DATA: `${API_BASE_URL}/api/user_auth-data`,
    GET_USER_BASE_DATA: `${API_BASE_URL}/api/user_base-data`,
    GET_USER_SELF_CHECK_DATA: `${API_BASE_URL}/api/user_self-check-data`,
    UPLOAD_AUTH_SOFTWARE: `${API_BASE_URL}/api/upload_auth-software`,
    UPLOAD_SELF_CHECK: `${API_BASE_URL}/api/upload_self-check`,
    GET_SELF_CHECK_DATA: `${API_BASE_URL}/api/get-self-check-data`,
    GET_AUTH_STATISTICS: `${API_BASE_URL}/api/statistics_auth`,
    GET_CHECK_STATISTICS: `${API_BASE_URL}/api/statistics_check`,
    GET_COMPUTER_STATISTICS: `${API_BASE_URL}/api/statistics_computer`,
    GET_SINGLE_DATA: `${API_BASE_URL}/api/auth-software`,
    DELETE_SELF_CHECK: `${API_BASE_URL}/api/delete_self_check`,
    UPDATE_SELF_CHECK: `${API_BASE_URL}/api/update_self_check`,
    DELETE_MY_SELECT_AUTH: `${API_BASE_URL}/api/user/auth-data/delete`,
    UPDATE_MY_SELECT_AUTH: `${API_BASE_URL}/api/update_auth`,
    ADD_COMPUTERINFO:`${API_BASE_URL}/api/add_computerinfo`,
    GET_COMPUTERINFO:`${API_BASE_URL}/api/get_computerinfo`,
    UPDATE_COMPUTERINFO:`${API_BASE_URL}/api/update_computerinfo`,
    GET_SOFTWARE_AUTH_RATE:`${API_BASE_URL}/api/statistics_software_auth_rate`,
    ADMIN_GET_ALL_USERS: `${API_BASE_URL}/api/admin/all-users`,
    ADMIN_DELETE_USER: `${API_BASE_URL}/api/admin/delete-user`,
    ADMIN_GET_ALL_SUB_USER: `${API_BASE_URL}/api/admin/get_all_subuser_sn`,
    
};


export default API_BASE_URL; 