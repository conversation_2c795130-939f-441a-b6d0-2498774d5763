const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');


// 添加本单位电脑数据
router.post('/api/add_computerinfo', authMiddleware, async (req, res) => {
    const { username, domestic_computer, not_domestic_computer } = req.body;
    if (!username || domestic_computer === undefined || not_domestic_computer === undefined) {
      return res.status(400).json({ success: false, message: '所有字段均为必填' });
    }
    let connection;
    try {
      connection = await pool.getConnection();
      // username唯一，使用REPLACE INTO或ON DUPLICATE KEY UPDATE
      const [result] = await connection.execute(
        'INSERT INTO computerinfo (username, domestic_computer, not_domestic_computer) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE domestic_computer = VALUES(domestic_computer), not_domestic_computer = VALUES(not_domestic_computer)',
        [username, domestic_computer, not_domestic_computer]
      );
      connection.release();
      res.status(200).json({ success: true, id: result.insertId });
    } catch (error) {
      if (connection) connection.release();
      console.error('添加电脑数据失败:', error);
      res.status(500).json({ success: false, message: '服务器错误' });
    }
  });


  module.exports = router;