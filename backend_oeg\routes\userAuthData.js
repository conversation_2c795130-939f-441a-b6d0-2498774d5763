const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');


// You will need to import your database connection or data access methods here
// const db = require('../path/to/your/db');

// POST endpoint to get software authorization data for a specific user
// 添加认证中间件保护此路由
router.post('/api/user_auth-data', authMiddleware, async (req, res) => {
  const { username } = req.body;

  if (!username) {
    return res.status(400).json({ message: 'Username is required' });
  }

  let connection;

  try {
    connection = await pool.getConnection();

    // Use prepared statement to prevent SQL injection
    const query = 'SELECT authID, owner, software_name, software_type, auth_number, auth_timelimit, buytime FROM authsoftware WHERE owner = ? order by authID DESC';
    const [rows] = await connection.execute(query, [username]);

    // The result is in 'rows'
    console.log(`Fetched software authorization data for user ${username}:`, rows);

    res.status(200).json(rows);

  } catch (error) {
    console.error(`Error fetching auth data for user ${username}:`, error);
    res.status(500).json({ message: 'Error fetching software authorization data', error: error.message });
  } finally {
    // Ensure the connection is released even if an error occurs
    if (connection) {
      connection.release();
    }
  }
});

module.exports = router;
