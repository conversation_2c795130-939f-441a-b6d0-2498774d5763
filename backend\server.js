const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const mysql = require('mysql2/promise'); // Using mysql2/promise for async/await
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken'); // 添加JWT支持
require('dotenv').config(); // Load environment variables
const multer = require('multer'); // Import multer
const xlsx = require('xlsx'); // Import xlsx
const path = require('path'); // Import path for file extension check

const app = express();
const port =  3001 //  3001;原来的端口 // 如果要在云端部署，修改为80 或者其他云端应用的端口号。// Use environment variable for port

// CORS 配置
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? ['http://zbh.fjsbqxh.com']
    : ['http://zbh.fjsbqxh.com', 'http://localhost:3007', 'http://*************'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400
};

// 中间件配置 - 确保这些中间件在所有路由之前
app.use(cors(corsOptions));

// 修改 body-parser 配置
app.use(express.json());  // 使用 express 内置的 json 解析器
app.use(express.urlencoded({ extended: true }));  // 使用 express 内置的 urlencoded 解析器

//指定静态路径
app.use(express.static(path.join(__dirname,'public')))

// 添加请求日志中间件（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log('收到请求:', {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      query: req.query
    });
    next();
  });
}

// --- Database Connection ---
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

let pool;

async function connectDatabase() {
  try {
    pool = mysql.createPool(dbConfig);
    if (process.env.NODE_ENV === 'development') {
      console.log('Database pool created successfully.');
      // Optional: Test the connection
      const [rows] = await pool.execute('SELECT 1 + 1 AS solution');
      console.log('Database connection test successful. 1 + 1 = ', rows[0].solution);
    }
  } catch (error) {
    console.error('Failed to connect to the database:', error);
    process.exit(1);
  }
}

connectDatabase(); // Establish database connection pool when the server starts

// 导入认证中间件
const authMiddleware = require('./middleware/auth');

// 移除重复的中间件，因为已经在上面配置过了

// --- Multer Setup for File Uploads ---
// Configure storage if you need to save files to disk temporarily
const storage = multer.memoryStorage(); // Store file in memory as a Buffer
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // Optional: limit file size (e.g., 10MB)
  fileFilter: (req, file, cb) => {
      // Basic file extension check
      const allowedExtensions = ['.xls', '.xlsx'];
      const fileExtension = path.extname(file.originalname).toLowerCase();
      if (allowedExtensions.includes(fileExtension)) {
          cb(null, true); // Accept the file
      } else {
          cb(new Error('Invalid file type. Only Excel files (.xls, .xlsx) are allowed.'), false); // Reject the file
      }
  }
});


// --- Login endpoint ---
app.post('/api/login', async (req, res) => {
  try {
    // 验证请求体
    if (!req.body || !req.body.username || !req.body.password) {
      return res.status(400).json({
        status: 'error',
        message: '请提供用户名和密码'
      });
    }

    const { username, password } = req.body;

    // 查找用户
    const [rows] = await pool.execute(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );

    const user = rows[0];

    // 检查用户是否存在
    if (!user) {
      return res.status(401).json({
        status: 'error',
        message: '用户名或密码错误'
      });
    }

    // 验证密码 - 所有用户都使用数据库中的哈希密码
    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      return res.status(401).json({
        status: 'error',
        message: '用户名或密码错误'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      {
      //  userId: user.id,
        username: user.username,
        short_name: user.short_name,
        //org_type: user.organization_type,
        //role: user.organization_type === "省版权局" ? "admin" : "user" // 根据组织类型设置角色
        role: (user.organization_type === "省版权局" || user.organization_type === "市版权局") ? "admin" : "user"
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );
    console.log('user.id is here = ....',user.id,user.username,user.short_name,user.organization_type);
    // 登录成功
    res.status(200).json({
      status: 'success',
      message: '登录成功',
      token,
      user: {
        username: user.username,
        email: user.email,
        address: user.address,
        short_name: user.short_name,
        mobile: user.mobile,
        contact: user.contact,
        //org_type: user.organization_type,
        //role: user.organization_type === "省版权局" ? "admin" : "user"
        role: (user.organization_type === "省版权局" || user.organization_type === "市版权局") ? "admin" : "user"
      }
    });

  } catch (error) {
    console.error('登录过程发生错误:', error);
    res.status(500).json({
      status: 'error',
      message: '登录过程发生错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// --- 令牌刷新端点 ---
app.post('/api/refresh-token', async (req, res) => {
  const { token } = req.body;
  
  if (!token) {
    return res.status(400).json({
      status: 'error',
      message: '未提供令牌'
    });
  }
  
  try {
    // 验证当前令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 检查令牌是否已过期
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp > currentTime) {
      // 令牌尚未过期，直接返回
      return res.status(200).json({
        status: 'success',
        message: '令牌有效',
        token
      });
    }
    
    // 生成新令牌
    const newToken = jwt.sign(
      {
        userId: decoded.userId,
        username: decoded.username,
        short_name: decoded.short_name,
        role: decoded.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );
    
    res.status(200).json({
      status: 'success',
      message: '令牌已刷新',
      token: newToken
    });
    
  } catch (error) {
    console.error('令牌刷新失败:', error);
    res.status(401).json({
      status: 'error',
      message: '无效的令牌',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});


//----- addUser endpoint -----

app.post('/api/adduser',async(req,res) => {
  const { username, email, password, mobile ,address,short_name,contact} = req.body; // The new user's short_name is 'short_name' here (W)

  // console.log(username,short_name);

  // Basic validation - 只要求必填字段
  if (!username || !password || !short_name) {
    return res.status(400).json({ message: '用户名、密码和用户简称为必填项' });
  }

  try {
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10); // 10 is the salt rounds

    // Insert new user into the database using the processedShortName
    const [result] = await pool.execute(
      'INSERT INTO users (username, email, password, mobile, address, short_name, contact) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [username, email || '', hashedPassword, mobile || '', address || '', short_name, contact || ''] // 允许空字段
    );

    // console.log('New user signed up:', { username, email });
    res.status(201).json({ message: 'User created successfully' });

  } catch (error) {
    console.error('Error during signup:', error);

    // Check for duplicate entry error (MySQL error code 1062)
    if (error.code === 'ER_DUP_ENTRY') {
        // You might want to check if it's email or username based on the error message
        // or rely on unique constraints and return a generic "user exists" error.
        return res.status(400).json({ message: 'User with this email or username already exists' });
    }

    res.status(500).json({ message: 'An error occurred during signup' });
  }
});


// ----- Update User Info endpoint -----
app.post('/api/update', authMiddleware, async (req, res) => { // 添加authMiddleware
  const { username, email, mobile, address, contact, old_username, short_name, password, organization_type } = req.body;
  if (!old_username) {
    return res.status(400).json({ message: '原用户名为必填项' });
  }

  // 检查是否是管理员，只有管理员才能修改organization_type
  if (organization_type && req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Forbidden: Only admins can change organization type' });
  }

  try {
    // 先获取当前用户的完整信息，包括 short_name
    const [currentUser] = await pool.execute(
      'SELECT short_name FROM users WHERE username = ?',
      [old_username]
    );
    
    // 构建动态更新查询
    let updateQuery = 'UPDATE users SET ';
    const updateParams = [];
    const updateFields = [];

    if (username) {
      updateFields.push('username = ?');
      updateParams.push(username);
    }
    if (email) {
      updateFields.push('email = ?');
      updateParams.push(email);
    }
    if (mobile) {
      updateFields.push('mobile = ?');
      updateParams.push(mobile);
    }
    if (address) {
      updateFields.push('address = ?');
      updateParams.push(address);
    }
    if (contact) {
      updateFields.push('contact = ?');
      updateParams.push(contact);
    }
    if (organization_type && req.user.role === 'admin') { // 只有管理员才能更新
      updateFields.push('organization_type = ?');
      updateParams.push(organization_type);
    }

    // If a new password is provided, hash it and add it to the update query
    if (password) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updateFields.push('password = ?');
      updateParams.push(hashedPassword);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({ message: '至少需要提供一个要更新的字段' });
    }

    updateQuery += updateFields.join(', ');
    updateQuery += ' WHERE username = ?';
    updateParams.push(old_username);

    const [result] = await pool.execute(updateQuery, updateParams);

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: '未找到该用户' });
    }
    
    // 返回更新后的用户信息，确保包含所有必要字段
    const userShortName = currentUser.length > 0 ? currentUser[0].short_name : short_name;
    
    // 获取更新后的完整用户信息
    const [updatedUser] = await pool.execute(
      'SELECT username, email, mobile, address, contact, short_name, organization_type FROM users WHERE username = ?',
      [username || old_username]
    );
    
    if (updatedUser.length === 0) {
      return res.status(404).json({ message: '更新后未找到用户信息' });
    }
    
    res.status(200).json({ 
      message: '用户信息更新成功', 
      success: true,
      user: updatedUser[0]
    });
  } catch (error) {
    console.error('用户信息更新失败:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ message: '用户名或邮箱已存在' });
    }
    res.status(500).json({ message: '服务器错误' });
  }
});


// ----- Get User Info endpoint -----
app.post('/api/get-user-info', async (req, res) => {
  const { username } = req.body;
  
  if (!username) {
    return res.status(400).json({ message: '用户名是必需的' });
  }

  try {
    // 查询用户信息
    const [rows] = await pool.execute(
      'SELECT username, email, mobile, address, contact, short_name FROM users WHERE username = ?',
      [username]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: '未找到该用户' });
    }

    // 返回用户信息（不包含密码）
    const user = rows[0];
    res.status(200).json({ 
      message: '获取用户信息成功', 
      user: user
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({ message: '服务器错误' });
  }
});


// ----- Get Users by Short Name Prefix endpoint -----
app.post('/api/by-shortname-prefix', async (req, res) => {
  const { prefix } = req.body;

  // Basic validation for prefix
  if (!prefix) {
    return res.status(400).json({ message: 'Prefix is required' });
  }

  try {
    // Query the database for users with short_name starting with the prefix
    // We select only the required fields and the short_name itself
    const [rows] = await pool.execute(
      'SELECT username, address, mobile, email, short_name,contact FROM users WHERE short_name LIKE ? ORDER BY short_name ASC',
      [prefix + '%'] // Use LIKE with % wildcard for prefix matching
    );

    // Return the found users
    // console.log(`Found ${rows.length} users with prefix: ${prefix}`);
    res.status(200).json(rows);

  } catch (error) {
    console.error('Error fetching users by prefix:', error);
    res.status(500).json({ message: 'An error occurred while fetching users.' }); // Corrected error message
  }
});


// Use the 'upload' middleware configured for single file upload with the field name 'authFile'
app.post('/api/auth-software', upload.single('authFile'), async (req, res) => {
  // req.file contains the uploaded file details
  // req.body contains other form data fields like 'owner'

  if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded.' });
  }

  const owner = req.body.owner; // Get owner from form data
  // console.log(owner);
  if (!owner) {
       // This check might be redundant if owner is always sent, but good practice
       console.warn('Owner information missing in upload request.');
      // return res.status(400).json({ message: 'Owner information is required.' });
  }


  try {
      // Read the Excel file from buffer
      const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
      const worksheet = workbook.Sheets[sheetName];

      // Convert sheet to array of objects
      // header: 1 means the first row is header, defval: '' provides default for empty cells
      const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1, defval: '' });

      // Assuming the first row in the Excel is headers: 软件名称, 软件类型, 授权数量, 授权时限
      // And data starts from the second row (index 1 in 0-indexed array)
      if (jsonData.length <= 1) {
           return res.status(400).json({ message: 'Excel file is empty or only contains headers.' });
      }

      // Map column headers to expected data (adjust indices if your Excel headers are different or in a different order)
      // This assumes a fixed order. A more robust way would be to find column index by header text.
      const headerRow = jsonData[0];
      const dataRows = jsonData.slice(1); // Get data rows starting from the second row

      // Simple mapping based on assumed column order:
      const softwareNameColIndex = 0; // Assuming 软件名称 is in the 1st column (index 0)
      const softwareTypeColIndex = 1; // Assuming 软件类型 is in the 2nd column (index 1)
      const authNumberColIndex = 2; // Assuming 授权数量 is in the 3rd column (index 2)
      const authTimelimitColIndex = 3; // Assuming 授权时限 is in the 4th column (index 3)
      const authBuytimeColIndex = 4; // Assuming 采购时间 is in the column (index 4)

      const valuesToInsert = [];
      for (const row of dataRows) {
          // Basic check if row has enough columns
          if (row.length > Math.max(softwareNameColIndex, softwareTypeColIndex, authNumberColIndex, authTimelimitColIndex, authBuytimeColIndex)) {
              const software_name = row[softwareNameColIndex]?.toString() || '';
              const software_type = row[softwareTypeColIndex]?.toString() || '';
              const auth_number = parseInt(row[authNumberColIndex], 10) || 0; // Parse number, default to 0 if invalid
              const auth_timelimit = parseInt(row[authTimelimitColIndex], 10) || 0; // Parse number, default to 0 if invalid
              const buytime = row[authBuytimeColIndex]?.toString() || ''; 

              // Only add rows with at least a software name
               if (owner) {
                   valuesToInsert.push([software_name, software_type, auth_number, auth_timelimit, buytime, owner]);
               }
          } else {
               console.warn('Skipping row due to insufficient columns:', row);
          }
      }

      if (valuesToInsert.length === 0) {
           return res.status(400).json({ message: 'No valid data rows found in the Excel file.' });
      }

      // --- Insert data into database ---
      const connection = await pool.getConnection(); // Get a connection from the pool
      try {
        //  await connection.beginTransaction(); // Start transaction

          // Prepare the INSERT statement
          const insertSql = 'INSERT INTO authsoftware (software_name, software_type, auth_number, auth_timelimit, buytime, owner) VALUES (?, ?, ?, ?, STR_TO_DATE(?, "%Y-%m-%d"), ?)';

          // Execute inserts for all rows
          for (const values of valuesToInsert) {
              await connection.execute(insertSql, values);
          }

          await connection.commit(); // Commit transaction
          connection.release(); // Release the connection

          res.status(200).json({ message: `Successfully uploaded and inserted ${valuesToInsert.length} records.` });

      } catch (dbError) {
          await connection.rollback(); // Rollback transaction on error
          connection.release(); // Release the connection
          console.error('Database transaction failed:', dbError);
          res.status(500).json({ message: 'Failed to save data to the database.' });
      }

  } catch (error) {
      console.error('Error processing Excel file:', error);
      res.status(500).json({ message: 'Error processing the uploaded file.' });
  }
});


// ----- Get Hierarchy Software Authorization Data Endpoint -----

app.post('/api/get-hierarchy-software-auth', async (req, res) => {
  const { loggedInUserShortName } = req.body;
  if (!loggedInUserShortName) {
    return res.status(400).json({ message: 'Logged-in user short name is required.' });
  }

  let connection;
  try {
    connection = await pool.getConnection();

    // 用于去重
    const visitedShortNames = new Set();
    const allUsernames = new Set();

    // 先查出当前用户自己的 username，并加入 allUsernames
    const [currentUserRows] = await connection.execute(
      'SELECT username FROM users WHERE short_name = ?',
      [loggedInUserShortName]
    );
    if (currentUserRows.length > 0) {
      allUsernames.add(currentUserRows[0].username);
    }

    // 递归函数
    async function fetchUsernamesByShortName(shortName) {
      if (visitedShortNames.has(shortName)) return;
      visitedShortNames.add(shortName);

      // 解析 X-Y-Z
      const parts = shortName.split('-');
      if (parts.length < 3) return;
      const x = parseInt(parts[0], 10);
      const z = parts[2];
      if (isNaN(x)) return;

      // 计算下一级前缀
      const nextPrefix = `${x + 1}-${z}`;

      // 查询所有以 nextPrefix 开头的用户
      const [users] = await connection.execute(
        'SELECT username, short_name FROM users WHERE short_name LIKE ?',
        [nextPrefix + '%']
      );

      for (const user of users) {
        if (!allUsernames.has(user.username)) {
          allUsernames.add(user.username);
          // 递归查找下一级
          await fetchUsernamesByShortName(user.short_name);
        }
      }
    }

    // 从当前用户简称开始递归
    await fetchUsernamesByShortName(loggedInUserShortName);

    if (allUsernames.size === 0) {
      connection.release();
      return res.status(200).json([]); // 没有找到
    }

    // 查询所有这些用户的授权数据
    const usernamesArr = Array.from(allUsernames);
    const placeholders = usernamesArr.map(() => '?').join(',');
    const [authData] = await connection.execute(
      `SELECT auth.*, u.short_name FROM authsoftware AS auth JOIN users u ON auth.owner = u.username WHERE auth.owner IN (${placeholders}) ORDER BY u.short_name, auth.buytime DESC`,
      usernamesArr
    );

    connection.release();
    res.status(200).json(authData);

  } catch (error) {
    if (connection) connection.release();
    console.error('Error fetching hierarchy software authorization data:', error);
    res.status(500).json({ message: 'An error occurred while fetching software authorization data.' });
  }
});

//-------get self check data --------
app.post('/api/get-self-check-data', async (req, res) => {
  const { owner } = req.body;
  if (!owner) {
    return res.status(400).json({ message: 'owner is required.' });
  }
  let connection;
  try {
    connection = await pool.getConnection();
    // 1. 查找起点用户的 short_name
    const [userRows] = await connection.execute(
      'SELECT short_name FROM users WHERE username = ?',
      [owner]
    );
    if (userRows.length === 0) {
      connection.release();
      return res.status(200).json([]);
    }
    const startShortName = userRows[0].short_name;
    const visitedShortNames = new Set();
    const allUsernames = new Set([owner]); // 包含自己
    // 递归查找
    async function fetchUsernamesByShortName(shortName) {
      if (visitedShortNames.has(shortName)) return;
      visitedShortNames.add(shortName);
      const parts = shortName.split('-');
      if (parts.length < 3) return;
      const x = parseInt(parts[0], 10);
      const z = parts[2];
      if (isNaN(x)) return;
      const nextPrefix = `${x + 1}-${z}`;
      const [users] = await connection.execute(
        'SELECT username, short_name FROM users WHERE short_name LIKE ?',
        [nextPrefix + '%']
      );
      for (const user of users) {
        if (!allUsernames.has(user.username)) {
          allUsernames.add(user.username);
          await fetchUsernamesByShortName(user.short_name);
        }
      }
    }
    await fetchUsernamesByShortName(startShortName);
    if (allUsernames.size === 0) {
      connection.release();
      return res.status(200).json([]);
    }
    const usernamesArr = Array.from(allUsernames);
    const placeholders = usernamesArr.map(() => '?').join(',');
    const [checkData] = await connection.execute(
      `SELECT self.*, u.short_name FROM checksoftware AS self JOIN users u ON self.checkedman = u.username WHERE self.checkedman IN (${placeholders}) ORDER BY u.short_name, self.check_time DESC`,
      usernamesArr
      //`SELECT * FROM checksoftware WHERE checkedman IN (${placeholders})`,
      //usernamesArr
    );
    


    connection.release();
    res.status(200).json(checkData);
  } catch (error) {
    if (connection) connection.release();
    console.error('Error fetching self-check data:', error);
    res.status(500).json({ message: 'An error occurred while fetching self-check data.' });
  }
});


// 删除指定用户的authid编号的软件授权数据
app.post('/api/user/auth-data/delete', async (req, res) => {
  const { authID } = req.body;
  if (!authID) {
    return res.status(400).json({ success: false, message: '缺少authID 参数' });
  }
  
  try {
    const [result] = await pool.execute(
      `DELETE FROM authsoftware
       WHERE authID = ? `,
      [authID]
    );
    if (result.affectedRows > 0) {
      res.json({ success: true });
    } else {
      //console.log(result)
      res.json({ success: false, message: '未找到要删除的数据' });
    }
  } catch (err) {
    res.json({ success: false, message: err.message });
  }
});



// ----- Update Software Authorization Data endpoint -----
app.post('/api/update_auth', async (req, res) => {
  const { authID, software_name, software_type, auth_number, auth_timelimit, buytime } = req.body;
  if (!authID || !software_name || !software_type || auth_number === undefined || auth_timelimit === undefined || !buytime) {
    return res.status(400).json({ success: false, message: '所有字段均为必填' });
  }
  try {
    const [result] = await pool.execute(
      `UPDATE authsoftware SET software_name = ?, software_type = ?, auth_number = ?, auth_timelimit = ?, buytime = ? WHERE authID = ?`,
      [software_name, software_type, auth_number, auth_timelimit, buytime, authID]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: '未找到该授权数据' });
    }
    res.status(200).json({ success: true, updated: { authID, software_name, software_type, auth_number, auth_timelimit, buytime } });
  } catch (error) {
    console.error('软件授权数据更新失败:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});


// 删除自查数据
app.post('/api/delete_self_check', async (req, res) => {
  const { checkID } = req.body;
  if (!checkID) {
    return res.status(400).json({ success: false, message: '缺少checkID参数' });
  }
  try {
    const [result] = await pool.execute(
      `DELETE FROM checksoftware WHERE checkID = ?`,
      [checkID]
    );
    if (result.affectedRows > 0) {
      res.json({ success: true });
    } else {
      res.json({ success: false, message: '未找到要删除的数据' });
    }
  } catch (err) {
    res.json({ success: false, message: err.message });
  }
});

// 编辑自查数据
app.post('/api/update_self_check', async (req, res) => {
  const { checkID, software_name, software_type, install_num, check_time } = req.body;
  if (!checkID || !software_name || !software_type || install_num === undefined || !check_time) {
    return res.status(400).json({ success: false, message: '所有字段均为必填' });
  }
  try {
    const [result] = await pool.execute(
      `UPDATE checksoftware SET software_name = ?, software_type = ?, install_num = ?, check_time = ? WHERE checkID = ?`,
      [software_name, software_type, install_num, check_time, checkID]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: '未找到该自查数据' });
    }
    res.status(200).json({ success: true, updated: { checkID, software_name, software_type, install_num, check_time } });
  } catch (error) {
    console.error('软件自查数据更新失败:', error);
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 导出 pool 和 authMiddleware 以便其他模块使用
module.exports = {
  pool,
  authMiddleware
};

// 引入并使用 authSoftwareUpload 路由
const authSoftwareUploadRouter = require('./routes/authSoftwareUpload');
app.use(authSoftwareUploadRouter);

// 引入并使用 selfCheckUpload 路由
const selfCheckUploadRouter = require('./routes/selfCheckUpload');
app.use(selfCheckUploadRouter);



// 引入并使用 statistics 路由
const statisticsRouter = require('./routes/statistics');
app.use(statisticsRouter);

// In your main backend file (e.g., server.js)
const userAuthDataRoutes = require('./routes/userAuthData');
const userSelfCheckDataRoutes = require('./routes/userSelfCheckData');
const userBaseDataRoutes = require('./routes/userBaseData');
const getAllSubUserRoutes = require('./routes/getAllSubUser');

app.use(userAuthDataRoutes);
app.use(userSelfCheckDataRoutes);
app.use(userBaseDataRoutes);
app.use(getAllSubUserRoutes);

const userComputerInfoRoutes = require('./routes/userComputerInfo');
app.use(userComputerInfoRoutes);

const addUserComputerRoutes = require('./routes/addUserComputer');
app.use(addUserComputerRoutes);



app.listen(port, () => {
  console.log(`Backend server running on http://localhost:${port}`);
}); 