const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');

//input short_name, return all subuser short_name
router.post('/api/admin/get_all_subuser_sn', authMiddleware, async (req, res) => {
    const { short_name } = req.body;
  
    // Basic validation for short_name
    if (!short_name) {
      return res.status(400).json({ message: 'short_name is required' });
    }
    
    try {
        const allShortNames = new Set();
        const visited = new Set(); // 防止循环引用

        async function recursiveByShortName(shortName) {
            // 防止循环引用和重复访问
            if (visited.has(shortName)) return;
            visited.add(shortName);
            
            allShortNames.add(shortName);
      
            // 解析 X-Y-Z 格式
            const parts = shortName.split('-');
            if (parts.length < 3) return;
            
            const x = parseInt(parts[0], 10);
            const z = parts[2];
            if (isNaN(x)) return;
      
            // 计算下一级前缀
            const nextPrefix = `${x + 1}-${z}`;
      
            // 查询所有以 nextPrefix 开头的用户
            const [rows] = await pool.execute(
              'SELECT short_name FROM users WHERE short_name LIKE ?',
              [nextPrefix + '%']
            );
      
            // 递归查找下一级
            for (const row of rows) {
              const childShortName = row.short_name;
              if (!visited.has(childShortName)) {
                await recursiveByShortName(childShortName);
              }
            }
        }

        await recursiveByShortName(short_name);

        res.status(200).json({
            success: true,
            data: Array.from(allShortNames),
            count: allShortNames.size
        });

    } catch (error) {
        console.error('Error in getAllSubUser:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Internal server error',
            error: error.message 
        });
    }
});

module.exports = router;
//通过输入short_name，来获取所有子节点的short_name；
//通过输入username,来获取所有子节点的username；
//通过输入short_name,来获取所有子节点的username；
//通过输入username,来获取所有子节点的short_name
