const express = require('express');
const router = express.Router();
const multer = require('multer');
const xlsx = require('xlsx');
const path = require('path');

// 从主服务导入 pool 和认证中间件
const { pool, authMiddleware } = require('../server');

// Multer 配置
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: (req, file, cb) => {
    const allowedExtensions = ['.xls', '.xlsx'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('文件格式错误.只能上传 Excel 文件 (.xls, .xlsx)。'), false);
    }
  }
});

const parseDate = (dateStr) => {
  if (!dateStr) return '';
  // 先尝试直接用
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return dateStr;
  // 兼容 2024/6/13 或 2024.6.13
  const parts = dateStr.split(/[-/.]/);
  // 移除敏感信息日志记录
  if (parts.length === 3) {
    let [y, m, d] = parts;
    if (y.length === 4) {
      // yyyy-mm-dd
      return `${y}-${m.padStart(2, '0')}-${d.padStart(2, '0')}`;
    }
    // mm-dd-yyyy 或 dd-mm-yyyy
    if (d.length === 4) {
      return `${d}-${m.padStart(2, '0')}-${y.padStart(2, '0')}`;
    }
  }
  return '';
};

// 上传自查数据API - 添加认证中间件
router.post('/api/upload_self-check', authMiddleware, upload.single('selfCheckFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded.' });
  }
  const checkedman = req.body.owner; // 当前登录用户名称
  // 移除敏感信息日志记录
  if (!checkedman) {
    return res.status(400).json({ message: 'Checkedman (owner) information is required.' });
  }
  try {
    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1, defval: '' });
    if (jsonData.length <= 1) {
      return res.status(400).json({ message: 'Excel文件无数据。' });
    }
    const headerRow = jsonData[7];//第8行是表头
    const dataRows = jsonData.slice(8); // 从第9行开始是数据
    // 假设列顺序为：自查时间、软件名称、软件类型、安装数量
    const checkTimeColIndex = 0;
    const softwareNameColIndex = 1;
    const softwareTypeColIndex = 2;
    const installNumColIndex = 3;
    const valuesToInsert = [];
    for (const row of dataRows) {
      if (row.length > Math.max(checkTimeColIndex, softwareNameColIndex, softwareTypeColIndex, installNumColIndex)) {
        const check_time = row[checkTimeColIndex]?.toString() || '';
        const software_name = row[softwareNameColIndex]?.toString() || '';
        const software_type = row[softwareTypeColIndex]?.toString() || '';
        const install_num = parseInt(row[installNumColIndex], 10) || 0;
        if (checkedman) {
          valuesToInsert.push([checkedman, check_time, software_name, software_type, install_num]);
          // 移除敏感信息日志记录
        }
      } else {
        // 移除敏感信息日志记录
      }
    }
    if (valuesToInsert.length === 0) {
      return res.status(400).json({ message: 'Excel文件中无有效数据' });
    }
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      const insertSql = 'INSERT INTO checksoftware (checkedman, check_time, software_name, software_type, install_num) VALUES (?, STR_TO_DATE(?, "%Y-%m-%d"), ?, ?, ?)'; //STR_TO_DATE(?, "%Y-%m-%d")
      for (const values of valuesToInsert) {
        await connection.execute(insertSql, values);
      }
      await connection.commit();
      connection.release();
      res.status(200).json({ message: `Successfully uploaded and inserted ${valuesToInsert.length} records.` });
    } catch (dbError) {
      await connection.rollback();
      connection.release();
      console.error('Database transaction failed');
      res.status(500).json({ message: 'Failed to save data to the database.' });
    }
  } catch (error) {
    console.error('Error processing Excel file');
    res.status(500).json({ message: 'Error processing the uploaded file.' });
  }
});

module.exports = router;
