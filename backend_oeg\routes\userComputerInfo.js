const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server');

// 查询当前用户电脑数据
router.post('/api/get_computerinfo', authMiddleware, async (req, res) => {
  const { username } = req.body;
  if (!username) return res.status(400).json({ success: false, message: '缺少用户名' });
  let connection;
  try {
    connection = await pool.getConnection();
    const [rows] = await connection.execute(
      'SELECT * FROM computerinfo WHERE username = ?',
      [username]
    );
    connection.release();
    res.status(200).json({ success: true, data: rows[0] || null });
  } catch (error) {
    if (connection) connection.release();
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

// 更新本单位电脑数据
router.post('/api/update_computerinfo', authMiddleware, async (req, res) => {
  const { username, domestic_computer, not_domestic_computer } = req.body;
  if (!username || domestic_computer === undefined || not_domestic_computer === undefined) {
    return res.status(400).json({ success: false, message: '所有字段均为必填' });
  }
  let connection;
  try {
    connection = await pool.getConnection();
    const [result] = await connection.execute(
      'UPDATE computerinfo SET domestic_computer = ?, not_domestic_computer = ? WHERE username = ?',
      [domestic_computer, not_domestic_computer, username]
    );
    connection.release();
    if (result.affectedRows > 0) {
      res.status(200).json({ success: true });
    } else {
      res.status(404).json({ success: false, message: '未找到该用户的电脑数据' });
    }
  } catch (error) {
    if (connection) connection.release();
    res.status(500).json({ success: false, message: '服务器错误' });
  }
});

module.exports = router; 