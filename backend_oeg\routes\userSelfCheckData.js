const express = require('express');
const router = express.Router();
const { pool, authMiddleware } = require('../server'); // 更新导入方式

// POST endpoint to get software self-check data for a specific user
// 添加认证中间件保护此路由
router.post('/api/user_self-check-data', authMiddleware, async (req, res) => {
  const { username } = req.body;

  if (!username) {
    return res.status(400).json({ message: 'Username is required' });
  }

  let connection; // Declare connection variable outside try block

  try {
    connection = await pool.getConnection();

    // Use prepared statement to prevent SQL injection
    // Assuming 'checkedman' is the column storing the username in selfcheckdata table
    const query = 'SELECT checkID, checkedman, check_time, software_type, software_name, install_num FROM checksoftware WHERE checkedman = ? order by checkID DESC';
    const [rows] = await connection.execute(query, [username]); // Pass username as a parameter

    // The result is in 'rows'
    // 移除敏感信息日志记录

    res.status(200).json(rows); // Return the fetched data

  } catch (error) {
    console.error(`Error fetching self-check data for user ${username}:`, error);
    res.status(500).json({ message: 'Error fetching software self-check data', error: error.message });
  } finally {
    // Ensure the connection is released even if an error occurs
    if (connection) {
      connection.release();
    }
  }
});



module.exports = router;
