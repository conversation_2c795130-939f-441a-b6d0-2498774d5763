const express = require('express');
const router = express.Router();
const multer = require('multer');
const xlsx = require('xlsx');
const path = require('path');

// 从主服务导入 pool 和认证中间件
const { pool, authMiddleware } = require('../server');

// Multer 配置
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: (req, file, cb) => {
    const allowedExtensions = ['.xls', '.xlsx'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('文件格式错误.只能上传 Excel 文件 (.xls, .xlsx)。'), false);
    }
  }
});

// 上传授权数据API - 添加认证中间件
router.post('/api/upload_auth-software', authMiddleware, upload.single('authFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded.' });
  }
  const owner = req.body.owner;
  if (!owner) {
    return res.status(400).json({ message: 'Owner information is required.' });
  }
  try {
    const workbook = xlsx.read(req.file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1, defval: '' });
    if (jsonData.length <= 1) {
      return res.status(400).json({ message: 'Excel 文件无数据' });
    }
    const headerRow = jsonData[7];
    const dataRows = jsonData.slice(8); // 从第8行开始
    const softwareNameColIndex = 0;
    const softwareTypeColIndex = 1;
    const authNumberColIndex = 2;
    const authTimelimitColIndex = 3;
    const authBuytimeColIndex = 4;
    
    const valuesToInsert = [];
    const invalidAuthTimelimitRows = []; // 记录授权期限无效的行号
    const invalidSoftwareTypeRows = []; // 记录软件类型为空的行号

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      const excelRowNumber = i + 9; // Excel中的实际行号

      if (row.length > Math.max(softwareNameColIndex, softwareTypeColIndex, authNumberColIndex, authTimelimitColIndex, authBuytimeColIndex)) {
        const software_name = row[softwareNameColIndex]?.toString() || '';
        const software_type = row[softwareTypeColIndex]?.toString() || '';
        
        // 验证 software_type 不能为空
        if (!software_type) {
          invalidSoftwareTypeRows.push(excelRowNumber);
          continue; // 跳过此行
        }

        const auth_number = parseInt(row[authNumberColIndex], 10) || 0;
        const auth_timelimit_raw = row[authTimelimitColIndex];
        const auth_timelimit = auth_timelimit_raw === '' ? null : parseInt(auth_timelimit_raw, 10);
        
        // 验证 auth_timelimit 必须是-1或1-100之间的整数
        if (auth_timelimit === null || isNaN(auth_timelimit) || (auth_timelimit !== -1 && (auth_timelimit < 1 || auth_timelimit > 100))) {
          invalidAuthTimelimitRows.push(excelRowNumber);
          continue; // 跳过此行
        }

        const buytime = row[authBuytimeColIndex]?.toString() || '';
        if (owner) {
          valuesToInsert.push([software_name, software_type, auth_number, auth_timelimit, buytime, owner]);
        }
      } else {
        // 移除敏感信息日志记录
      }
    }

    let warningMessages = [];
    if (invalidSoftwareTypeRows.length > 0) {
        warningMessages.push(`检测到 ${invalidSoftwareTypeRows.length} 行的“软件类型”为空，已被跳过。无效行号: ${invalidSoftwareTypeRows.join(', ')}。`);
    }
    if (invalidAuthTimelimitRows.length > 0) {
        warningMessages.push(`检测到 ${invalidAuthTimelimitRows.length} 行的“授权期限”值无效，已被跳过。无效行号: ${invalidAuthTimelimitRows.join(', ')}。`);
    }

    if (valuesToInsert.length === 0) {
      let message = 'Excel 表中没有有效的数据。';
      if (warningMessages.length > 0) {
        message = '上传的文件中没有有效数据。\n' + warningMessages.join('\n');
      }
      return res.status(400).json({ message });
    }

    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();
      const insertSql = 'INSERT INTO authsoftware (software_name, software_type, auth_number, auth_timelimit, buytime, owner) VALUES (?, ?, ?, ?, STR_TO_DATE(?, "%Y-%m-%d"), ?)';
      for (const values of valuesToInsert) {
        await connection.execute(insertSql, values);
      }
      await connection.commit();
      connection.release();

      let message = `成功上传并插入 ${valuesToInsert.length} 条记录。`;
      if (warningMessages.length > 0) {
        message += `\n\n警告：\n` + warningMessages.join('\n');
      }
      res.status(200).json({ message });

    } catch (dbError) {
      await connection.rollback();
      connection.release();
      console.error('Database transaction failed');
      res.status(500).json({ message: 'Failed to save data to the database.' });
    }
  } catch (error) {
    console.error('Error processing Excel file');
    res.status(500).json({ message: 'Error processing the uploaded file.' });
  }
});

module.exports = router;
