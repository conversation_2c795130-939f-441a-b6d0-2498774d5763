[{"F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\index.js": "1", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\reportWebVitals.js": "2", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\App.js": "3", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\LoginForm.js": "4", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\config.js": "5", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\LoggedInPage.js": "6", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\ComputerDataForm.js": "7", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditSelfCheckForm.js": "8", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\CompleteUserInfoForm.js": "9", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditUserForm.js": "10", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditAuthForm.js": "11", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\UserDetailsModal.js": "12", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditComputerForm.js": "13", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\SystemAdminPage.js": "14", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\AddUserForm.js": "15", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\AdminDataManagement.js": "16", "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\AdminUserManagement.js": "17", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\index.js": "18", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\reportWebVitals.js": "19", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\App.js": "20", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\config.js": "21", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\LoginForm.js": "22", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\LoggedInPage.js": "23", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\CompleteUserInfoForm.js": "24", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\AddUserForm.js": "25", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\ComputerDataForm.js": "26", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditUserForm.js": "27", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditSelfCheckForm.js": "28", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditAuthForm.js": "29", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditComputerForm.js": "30", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\UserDetailsModal.js": "31", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\SystemAdminPage.js": "32", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\AdminUserManagement.js": "33", "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\AdminDataManagement.js": "34"}, {"size": 535, "mtime": 1747835810000, "results": "35", "hashOfConfig": "36"}, {"size": 362, "mtime": 1747835810000, "results": "37", "hashOfConfig": "36"}, {"size": 4992, "mtime": 1753672991000, "results": "38", "hashOfConfig": "36"}, {"size": 6824, "mtime": 1751862345000, "results": "39", "hashOfConfig": "36"}, {"size": 2191, "mtime": 1753617976000, "results": "40", "hashOfConfig": "36"}, {"size": 55991, "mtime": 1753839994653, "results": "41", "hashOfConfig": "36"}, {"size": 2299, "mtime": 1751638219000, "results": "42", "hashOfConfig": "36"}, {"size": 3109, "mtime": 1750669888000, "results": "43", "hashOfConfig": "36"}, {"size": 5474, "mtime": 1751930592000, "results": "44", "hashOfConfig": "36"}, {"size": 5489, "mtime": 1753624589000, "results": "45", "hashOfConfig": "36"}, {"size": 3909, "mtime": 1750843084000, "results": "46", "hashOfConfig": "36"}, {"size": 3688, "mtime": 1753453157000, "results": "47", "hashOfConfig": "36"}, {"size": 3177, "mtime": 1753435833000, "results": "48", "hashOfConfig": "36"}, {"size": 1227, "mtime": 1753369515000, "results": "49", "hashOfConfig": "36"}, {"size": 8787, "mtime": 1751859757000, "results": "50", "hashOfConfig": "36"}, {"size": 522, "mtime": 1753369936000, "results": "51", "hashOfConfig": "36"}, {"size": 6885, "mtime": 1753773737000, "results": "52", "hashOfConfig": "36"}, {"size": 535, "mtime": 1747835810000, "results": "53", "hashOfConfig": "54"}, {"size": 362, "mtime": 1747835810000, "results": "55", "hashOfConfig": "54"}, {"size": 4992, "mtime": 1753672991000, "results": "56", "hashOfConfig": "54"}, {"size": 2267, "mtime": 1754150426994, "results": "57", "hashOfConfig": "54"}, {"size": 6824, "mtime": 1751862345000, "results": "58", "hashOfConfig": "54"}, {"size": 55991, "mtime": 1753839994653, "results": "59", "hashOfConfig": "54"}, {"size": 5474, "mtime": 1751930592000, "results": "60", "hashOfConfig": "54"}, {"size": 8787, "mtime": 1751859757000, "results": "61", "hashOfConfig": "54"}, {"size": 2299, "mtime": 1751638219000, "results": "62", "hashOfConfig": "54"}, {"size": 5489, "mtime": 1753624589000, "results": "63", "hashOfConfig": "54"}, {"size": 3109, "mtime": 1750669888000, "results": "64", "hashOfConfig": "54"}, {"size": 3909, "mtime": 1750843084000, "results": "65", "hashOfConfig": "54"}, {"size": 3177, "mtime": 1753435833000, "results": "66", "hashOfConfig": "54"}, {"size": 3688, "mtime": 1753453157000, "results": "67", "hashOfConfig": "54"}, {"size": 1227, "mtime": 1753369515000, "results": "68", "hashOfConfig": "54"}, {"size": 6946, "mtime": 1754151120572, "results": "69", "hashOfConfig": "54"}, {"size": 522, "mtime": 1753369936000, "results": "70", "hashOfConfig": "54"}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fkybvx", {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lo70y5", {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\index.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\reportWebVitals.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\App.js", ["173"], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\LoginForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\config.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\LoggedInPage.js", ["174", "175", "176", "177", "178", "179", "180", "181", "182"], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\ComputerDataForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditSelfCheckForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\CompleteUserInfoForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditUserForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditAuthForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\UserDetailsModal.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\EditComputerForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\SystemAdminPage.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\AddUserForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\AdminDataManagement.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版\\frontend\\src\\AdminUserManagement.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\index.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\reportWebVitals.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\App.js", ["183"], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\config.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\LoginForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\LoggedInPage.js", ["184", "185", "186", "187", "188", "189", "190", "191", "192"], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\CompleteUserInfoForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\AddUserForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\ComputerDataForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditUserForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditSelfCheckForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditAuthForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\EditComputerForm.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\UserDetailsModal.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\SystemAdminPage.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\AdminUserManagement.js", [], [], "F:\\正版化平台项目\\zbh-0801开发版 - 副本\\frontend\\src\\AdminDataManagement.js", [], [], {"ruleId": "193", "severity": 1, "message": "194", "line": 94, "column": 6, "nodeType": "195", "endLine": 94, "endColumn": 82, "suggestions": "196"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 18, "column": 33, "nodeType": "199", "messageId": "200", "endLine": 18, "endColumn": 57}, {"ruleId": "197", "severity": 1, "message": "201", "line": 20, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 20, "endColumn": 28}, {"ruleId": "193", "severity": 1, "message": "202", "line": 111, "column": 6, "nodeType": "195", "endLine": 111, "endColumn": 22, "suggestions": "203"}, {"ruleId": "197", "severity": 1, "message": "204", "line": 144, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 144, "endColumn": 26}, {"ruleId": "193", "severity": 1, "message": "205", "line": 171, "column": 6, "nodeType": "195", "endLine": 171, "endColumn": 22, "suggestions": "206"}, {"ruleId": "197", "severity": 1, "message": "207", "line": 463, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 463, "endColumn": 32}, {"ruleId": "197", "severity": 1, "message": "208", "line": 595, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 595, "endColumn": 37}, {"ruleId": "197", "severity": 1, "message": "209", "line": 776, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 776, "endColumn": 30}, {"ruleId": "197", "severity": 1, "message": "210", "line": 884, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 884, "endColumn": 33}, {"ruleId": "193", "severity": 1, "message": "194", "line": 94, "column": 6, "nodeType": "195", "endLine": 94, "endColumn": 82, "suggestions": "211"}, {"ruleId": "197", "severity": 1, "message": "198", "line": 18, "column": 33, "nodeType": "199", "messageId": "200", "endLine": 18, "endColumn": 57}, {"ruleId": "197", "severity": 1, "message": "201", "line": 20, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 20, "endColumn": 28}, {"ruleId": "193", "severity": 1, "message": "202", "line": 111, "column": 6, "nodeType": "195", "endLine": 111, "endColumn": 22, "suggestions": "212"}, {"ruleId": "197", "severity": 1, "message": "204", "line": 144, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 144, "endColumn": 26}, {"ruleId": "193", "severity": 1, "message": "205", "line": 171, "column": 6, "nodeType": "195", "endLine": 171, "endColumn": 22, "suggestions": "213"}, {"ruleId": "197", "severity": 1, "message": "207", "line": 463, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 463, "endColumn": 32}, {"ruleId": "197", "severity": 1, "message": "208", "line": 595, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 595, "endColumn": 37}, {"ruleId": "197", "severity": 1, "message": "209", "line": 776, "column": 10, "nodeType": "199", "messageId": "200", "endLine": 776, "endColumn": 30}, {"ruleId": "197", "severity": 1, "message": "210", "line": 884, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 884, "endColumn": 33}, "react-hooks/exhaustive-deps", "React Hook useCallback has an unnecessary dependency: 'API_ENDPOINTS.REFRESH_TOKEN'. Either exclude it or remove the dependency array. Outer scope values like 'API_ENDPOINTS.REFRESH_TOKEN' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["214"], "no-unused-vars", "'setIsDataManagerExpanded' is assigned a value but never used.", "Identifier", "unusedVar", "'dataManagementView' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'selectedUserAuthData'. Either include it or remove the dependency array.", ["215"], "'fetchUserBaseData' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'selectedUserBaseData'. Either include it or remove the dependency array.", ["216"], "'handleShowAuthDataClick' is assigned a value but never used.", "'handleShowSelfCheckDataClick' is assigned a value but never used.", "'showComputerDataForm' is assigned a value but never used.", "'handleComputerDataSubmit' is assigned a value but never used.", ["217"], ["218"], ["219"], {"desc": "220", "fix": "221"}, {"desc": "222", "fix": "223"}, {"desc": "224", "fix": "225"}, {"desc": "220", "fix": "226"}, {"desc": "222", "fix": "227"}, {"desc": "224", "fix": "228"}, "Update the dependencies array to be: [token, parseJwt, setupAutoLogout, handleLogout]", {"range": "229", "text": "230"}, "Update the dependencies array to be: [getAuthHeaders, selectedUserAuthData]", {"range": "231", "text": "232"}, "Update the dependencies array to be: [getAuthHeaders, selectedUserBaseData]", {"range": "233", "text": "234"}, {"range": "235", "text": "230"}, {"range": "236", "text": "232"}, {"range": "237", "text": "234"}, [2552, 2628], "[token, parseJwt, setupAutoLogout, handleLogout]", [5051, 5067], "[get<PERSON><PERSON><PERSON><PERSON><PERSON>, selectedUserAuthData]", [7094, 7110], "[get<PERSON><PERSON><PERSON><PERSON><PERSON>, selectedUserBaseData]", [2552, 2628], [5051, 5067], [7094, 7110]]