{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u6B63\\u7248\\u5316\\u5E73\\u53F0\\u9879\\u76EE\\\\zbh-0801\\u5F00\\u53D1\\u7248 - \\u526F\\u672C\\\\frontend\\\\src\\\\AdminUserManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { API_ENDPOINTS } from './config';\nimport EditUserForm from './EditUserForm'; // 引入EditUserForm组件\nimport './SystemAdminPage.css'; // 引入样式\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminUserManagement({\n  token\n}) {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [sortOrder, setSortOrder] = useState('asc'); // 'asc' or 'desc'\n  const [selectedType, setSelectedType] = useState('全部'); // New state for filtering\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false); // 控制编辑模态框的显示\n  const [editingUser, setEditingUser] = useState(null); // 存储正在编辑的用户\n  // const [organizationTypes, setOrganizationTypes] = useState([]);\n\n  const getAuthHeaders = useCallback(() => {\n    return {\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    };\n  }, [token]);\n  const fetchAllUsers = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(API_ENDPOINTS.ADMIN_GET_ALL_SUB_USER, {\n        method: 'POST',\n        headers: getAuthHeaders(),\n        body: JSON.stringify({\n          short_name: '1-bqj-fzbqj'\n        })\n      });\n      if (!response.ok) {\n        throw new Error('Failed to fetch users.');\n      }\n      const data = await response.json();\n      setUsers(data);\n      console.log(data);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [getAuthHeaders]);\n  useEffect(() => {\n    fetchAllUsers();\n  }, [fetchAllUsers]);\n\n  // Function to toggle sort order\n  const handleSort = () => {\n    setSortOrder(currentOrder => currentOrder === 'asc' ? 'desc' : 'asc');\n  };\n  const handleEdit = user => {\n    setEditingUser(user);\n    setIsEditModalOpen(true);\n  };\n  const handleDelete = async userToDelete => {\n    if (!userToDelete || !userToDelete.short_name) {\n      alert('用户信息不完整，无法进行删除操作。');\n      return;\n    }\n    const parts = userToDelete.short_name.split('-');\n    if (parts.length >= 3) {\n      const level = parseInt(parts[0], 10);\n      const parentIdentifier = parts[2];\n      const childPrefix = `${level + 1}-${parentIdentifier}`;\n      const hasChildren = users.some(user => user.short_name && user.short_name.startsWith(childPrefix));\n      if (hasChildren) {\n        alert('该用户存在下一级用户，无法删除。');\n        return;\n      }\n    }\n    if (window.confirm(`确定要删除用户 “${userToDelete.username}” 吗？`)) {\n      try {\n        const response = await fetch(API_ENDPOINTS.ADMIN_DELETE_USER, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n          body: JSON.stringify({\n            username: userToDelete.username\n          })\n        });\n        if (!response.ok) {\n          const errorData = await response.json().catch(() => ({}));\n          throw new Error(errorData.message || '删除用户失败');\n        }\n        setUsers(users.filter(user => user.username !== userToDelete.username));\n        alert('用户删除成功');\n      } catch (err) {\n        setError(err.message);\n        alert(`删除失败: ${err.message}`);\n      }\n    }\n  };\n  const handleCloseModal = () => {\n    setIsEditModalOpen(false);\n    setEditingUser(null);\n  };\n  const handleUpdateSuccess = updatedUser => {\n    // 更新用户列表中的用户信息\n    setUsers(users.map(user => user.username === updatedUser.username ? updatedUser : user));\n    handleCloseModal(); // 关闭模态框\n  };\n\n  // Get unique organization types for the filter dropdown\n  const organizationTypes = useMemo(() => {\n    const types = new Set(users.map(user => user.organization_type));\n    return ['全部', ...Array.from(types)];\n  }, [users]);\n\n  // Memoize the filtered and sorted users\n  const filteredAndSortedUsers = useMemo(() => {\n    // 1. Filter users based on selectedType\n    const filtered = selectedType === '全部' ? users : users.filter(user => user.organization_type === selectedType);\n\n    // 2. Sort the filtered users\n    const sorted = [...filtered].sort((a, b) => {\n      const typeA = a.organization_type || '';\n      const typeB = b.organization_type || '';\n      const nameA = a.username || '';\n      const nameB = b.username || '';\n      if (typeA < typeB) return -1;\n      if (typeA > typeB) return 1;\n      if (nameA < nameB) return -1;\n      if (nameA > nameB) return 1;\n      return 0;\n    });\n    if (sortOrder === 'desc') {\n      return sorted.reverse();\n    }\n    return sorted;\n  }, [users, sortOrder, selectedType]);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u7528\\u6237...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: [\"\\u9519\\u8BEF: \", error]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"\\u6240\\u6709\\u7528\\u6237\\u5217\\u8868\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginLeft: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"type-filter\",\n          children: \"\\u6309\\u7528\\u6237\\u7C7B\\u522B\\u7B5B\\u9009: \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"type-filter\",\n          value: selectedType,\n          onChange: e => setSelectedType(e.target.value),\n          children: organizationTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: type,\n            children: type\n          }, type, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"user-table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u5355\\u4F4D\\u5730\\u5740\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u8054\\u7CFB\\u4EBA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u8054\\u7CFB\\u7535\\u8BDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u7535\\u5B50\\u90AE\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            onClick: handleSort,\n            style: {\n              cursor: 'pointer'\n            },\n            children: [\"\\u7528\\u6237\\u7C7B\\u522B \", sortOrder === 'asc' ? '▲' : '▼']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\u64CD\\u4F5C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: filteredAndSortedUsers.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.address\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.contact\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.mobile\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: user.organization_type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleEdit(user),\n              children: \"\\u7F16\\u8F91\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDelete(user),\n              children: \"\\u5220\\u9664\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, user.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), isEditModalOpen && editingUser && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: /*#__PURE__*/_jsxDEV(EditUserForm, {\n          user: editingUser,\n          onCancel: handleCloseModal,\n          onSuccess: handleUpdateSuccess,\n          token: token,\n          isAdmin: true // 传递isAdmin属性\n          ,\n          organizationTypes: organizationTypes.filter(type => type !== '全部')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminUserManagement, \"77NcwceOLlT+ExZXE21UhWJ1K4g=\");\n_c = AdminUserManagement;\nexport default AdminUserManagement;\nvar _c;\n$RefreshReg$(_c, \"AdminUserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "API_ENDPOINTS", "EditUserForm", "jsxDEV", "_jsxDEV", "AdminUserManagement", "token", "_s", "users", "setUsers", "loading", "setLoading", "error", "setError", "sortOrder", "setSortOrder", "selectedType", "setSelectedType", "isEditModalOpen", "setIsEditModalOpen", "editingUser", "setEditingUser", "getAuthHeaders", "fetchAllUsers", "response", "fetch", "ADMIN_GET_ALL_SUB_USER", "method", "headers", "body", "JSON", "stringify", "short_name", "ok", "Error", "data", "json", "console", "log", "err", "message", "handleSort", "currentOrder", "handleEdit", "user", "handleDelete", "userToDelete", "alert", "parts", "split", "length", "level", "parseInt", "parentIdentifier", "childPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "startsWith", "window", "confirm", "username", "ADMIN_DELETE_USER", "errorData", "catch", "filter", "handleCloseModal", "handleUpdateSuccess", "updatedUser", "map", "organizationTypes", "types", "Set", "organization_type", "Array", "from", "filteredAndSortedUsers", "filtered", "sorted", "sort", "a", "b", "typeA", "typeB", "nameA", "nameB", "reverse", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "alignItems", "marginBottom", "marginLeft", "htmlFor", "id", "value", "onChange", "e", "target", "type", "className", "onClick", "cursor", "address", "contact", "mobile", "email", "onCancel", "onSuccess", "isAdmin", "_c", "$RefreshReg$"], "sources": ["F:/正版化平台项目/zbh-0801开发版 - 副本/frontend/src/AdminUserManagement.js"], "sourcesContent": ["\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { API_ENDPOINTS } from './config';\nimport EditUserForm from './EditUserForm'; // 引入EditUserForm组件\nimport './SystemAdminPage.css'; // 引入样式\n\nfunction AdminUserManagement({ token }) {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [sortOrder, setSortOrder] = useState('asc'); // 'asc' or 'desc'\n  const [selectedType, setSelectedType] = useState('全部'); // New state for filtering\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false); // 控制编辑模态框的显示\n  const [editingUser, setEditingUser] = useState(null); // 存储正在编辑的用户\n // const [organizationTypes, setOrganizationTypes] = useState([]);\n\n  const getAuthHeaders = useCallback(() => {\n    return {\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    };\n  }, [token]);\n\n  const fetchAllUsers = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(API_ENDPOINTS.ADMIN_GET_ALL_SUB_USER, {\n        method: 'POST',\n        headers: getAuthHeaders(),\n        body:JSON.stringify({short_name:'1-bqj-fzbqj'}),\n      });\n      if (!response.ok) {\n        throw new Error('Failed to fetch users.');\n      }\n      const data = await response.json();\n      setUsers(data);\n      console.log(data);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [getAuthHeaders]);\n\n   useEffect(() => {\n      fetchAllUsers();\n    }, [fetchAllUsers]);\n    \n  // Function to toggle sort order\n  const handleSort = () => {\n    setSortOrder(currentOrder => (currentOrder === 'asc' ? 'desc' : 'asc'));\n  };\n\n  const handleEdit = (user) => {\n    setEditingUser(user);\n    setIsEditModalOpen(true);\n  };\n\n  const handleDelete = async (userToDelete) => {\n    if (!userToDelete || !userToDelete.short_name) {\n      alert('用户信息不完整，无法进行删除操作。');\n      return;\n    }\n\n    const parts = userToDelete.short_name.split('-');\n    if (parts.length >= 3) {\n      const level = parseInt(parts[0], 10);\n      const parentIdentifier = parts[2];\n      const childPrefix = `${level + 1}-${parentIdentifier}`;\n\n      const hasChildren = users.some(user => \n        user.short_name && user.short_name.startsWith(childPrefix)\n      );\n\n      if (hasChildren) {\n        alert('该用户存在下一级用户，无法删除。');\n        return;\n      }\n    }\n\n    if (window.confirm(`确定要删除用户 “${userToDelete.username}” 吗？`)) {\n      try {\n        const response = await fetch(API_ENDPOINTS.ADMIN_DELETE_USER, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n          body: JSON.stringify({ username: userToDelete.username }),\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json().catch(() => ({}));\n          throw new Error(errorData.message || '删除用户失败');\n        }\n\n        setUsers(users.filter(user => user.username !== userToDelete.username));\n        alert('用户删除成功');\n      } catch (err) {\n        setError(err.message);\n        alert(`删除失败: ${err.message}`);\n      }\n    }\n  };\n\n  const handleCloseModal = () => {\n    setIsEditModalOpen(false);\n    setEditingUser(null);\n  };\n\n  const handleUpdateSuccess = (updatedUser) => {\n    // 更新用户列表中的用户信息\n    setUsers(users.map(user => (user.username === updatedUser.username ? updatedUser : user)));\n    handleCloseModal(); // 关闭模态框\n  };\n\n    // Get unique organization types for the filter dropdown\n    const organizationTypes = useMemo(() => {\n      const types = new Set(users.map(user => user.organization_type));\n      return ['全部', ...Array.from(types)];\n    }, [users]);\n\n  // Memoize the filtered and sorted users\n  const filteredAndSortedUsers = useMemo(() => {\n    // 1. Filter users based on selectedType\n    const filtered = selectedType === '全部'\n      ? users\n      : users.filter(user => user.organization_type === selectedType);\n\n    // 2. Sort the filtered users\n    const sorted = [...filtered].sort((a, b) => {\n      const typeA = a.organization_type || '';\n      const typeB = b.organization_type || '';\n      const nameA = a.username || '';\n      const nameB = b.username || '';\n\n      if (typeA < typeB) return -1;\n      if (typeA > typeB) return 1;\n      if (nameA < nameB) return -1;\n      if (nameA > nameB) return 1;\n      return 0;\n    });\n\n    if (sortOrder === 'desc') {\n      return sorted.reverse();\n    }\n    return sorted;\n  }, [users, sortOrder, selectedType]);\n\n  if (loading) return <p>正在加载用户...</p>;\n  if (error) return <p>错误: {error}</p>;\n\n  return (\n    <div>\n      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>\n        <h4>所有用户列表</h4>\n        <div style={{ marginLeft: '20px' }}>\n          <label htmlFor=\"type-filter\">按用户类别筛选: </label>\n          <select \n            id=\"type-filter\"\n            value={selectedType}\n            onChange={e => setSelectedType(e.target.value)}\n          >\n            {organizationTypes.map(type => (\n              <option key={type} value={type}>{type}</option>\n            ))}\n          </select>\n        </div>\n      </div>\n      <table className=\"user-table\">\n        <thead>\n          <tr>\n            <th>用户名</th>\n            <th>单位地址</th>\n            <th>联系人</th>\n            <th>联系电话</th>\n            <th>电子邮件</th>\n            <th onClick={handleSort} style={{ cursor: 'pointer' }}>\n              用户类别 {sortOrder === 'asc' ? '▲' : '▼'}\n            </th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          {filteredAndSortedUsers.map(user => (\n            <tr key={user.id}>\n              <td>{user.username}</td>\n              <td>{user.address}</td>\n              <td>{user.contact}</td>\n              <td>{user.mobile}</td>\n              <td>{user.email}</td>\n              <td>{user.organization_type}</td>\n              <td>\n                <button onClick={() => handleEdit(user)}>编辑</button>\n                <button onClick={() => handleDelete(user)}>删除</button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n\n      {isEditModalOpen && editingUser && (\n        <div className=\"modal-overlay\">\n          <div className=\"modal-content\">\n            <EditUserForm\n              user={editingUser}\n              onCancel={handleCloseModal}\n              onSuccess={handleUpdateSuccess}\n              token={token}\n              isAdmin={true} // 传递isAdmin属性\n              organizationTypes={organizationTypes.filter(type => type !== '全部')}\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default AdminUserManagement;\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,aAAa,QAAQ,UAAU;AACxC,OAAOC,YAAY,MAAM,gBAAgB,CAAC,CAAC;AAC3C,OAAO,uBAAuB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEhC,SAASC,mBAAmBA,CAAC;EAAEC;AAAM,CAAC,EAAE;EAAAC,EAAA;EACtC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACvD;;EAEC,MAAMyB,cAAc,GAAGvB,WAAW,CAAC,MAAM;IACvC,OAAO;MACL,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUO,KAAK;IAClC,CAAC;EACH,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,MAAMiB,aAAa,GAAGxB,WAAW,CAAC,YAAY;IAC5CY,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAACxB,aAAa,CAACyB,sBAAsB,EAAE;QACjEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAEN,cAAc,CAAC,CAAC;QACzBO,IAAI,EAACC,IAAI,CAACC,SAAS,CAAC;UAACC,UAAU,EAAC;QAAa,CAAC;MAChD,CAAC,CAAC;MACF,IAAI,CAACR,QAAQ,CAACS,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClC3B,QAAQ,CAAC0B,IAAI,CAAC;MACdE,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACW,cAAc,CAAC,CAAC;EAEnBxB,SAAS,CAAC,MAAM;IACbyB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAErB;EACA,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvB1B,YAAY,CAAC2B,YAAY,IAAKA,YAAY,KAAK,KAAK,GAAG,MAAM,GAAG,KAAM,CAAC;EACzE,CAAC;EAED,MAAMC,UAAU,GAAIC,IAAI,IAAK;IAC3BvB,cAAc,CAACuB,IAAI,CAAC;IACpBzB,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM0B,YAAY,GAAG,MAAOC,YAAY,IAAK;IAC3C,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACd,UAAU,EAAE;MAC7Ce,KAAK,CAAC,mBAAmB,CAAC;MAC1B;IACF;IAEA,MAAMC,KAAK,GAAGF,YAAY,CAACd,UAAU,CAACiB,KAAK,CAAC,GAAG,CAAC;IAChD,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,MAAMC,KAAK,GAAGC,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACpC,MAAMK,gBAAgB,GAAGL,KAAK,CAAC,CAAC,CAAC;MACjC,MAAMM,WAAW,GAAG,GAAGH,KAAK,GAAG,CAAC,IAAIE,gBAAgB,EAAE;MAEtD,MAAME,WAAW,GAAG/C,KAAK,CAACgD,IAAI,CAACZ,IAAI,IACjCA,IAAI,CAACZ,UAAU,IAAIY,IAAI,CAACZ,UAAU,CAACyB,UAAU,CAACH,WAAW,CAC3D,CAAC;MAED,IAAIC,WAAW,EAAE;QACfR,KAAK,CAAC,kBAAkB,CAAC;QACzB;MACF;IACF;IAEA,IAAIW,MAAM,CAACC,OAAO,CAAC,YAAYb,YAAY,CAACc,QAAQ,MAAM,CAAC,EAAE;MAC3D,IAAI;QACF,MAAMpC,QAAQ,GAAG,MAAMC,KAAK,CAACxB,aAAa,CAAC4D,iBAAiB,EAAE;UAC5DlC,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAEN,cAAc,CAAC,CAAC;UACzBO,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAE6B,QAAQ,EAAEd,YAAY,CAACc;UAAS,CAAC;QAC1D,CAAC,CAAC;QAEF,IAAI,CAACpC,QAAQ,CAACS,EAAE,EAAE;UAChB,MAAM6B,SAAS,GAAG,MAAMtC,QAAQ,CAACY,IAAI,CAAC,CAAC,CAAC2B,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;UACzD,MAAM,IAAI7B,KAAK,CAAC4B,SAAS,CAACtB,OAAO,IAAI,QAAQ,CAAC;QAChD;QAEA/B,QAAQ,CAACD,KAAK,CAACwD,MAAM,CAACpB,IAAI,IAAIA,IAAI,CAACgB,QAAQ,KAAKd,YAAY,CAACc,QAAQ,CAAC,CAAC;QACvEb,KAAK,CAAC,QAAQ,CAAC;MACjB,CAAC,CAAC,OAAOR,GAAG,EAAE;QACZ1B,QAAQ,CAAC0B,GAAG,CAACC,OAAO,CAAC;QACrBO,KAAK,CAAC,SAASR,GAAG,CAACC,OAAO,EAAE,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMyB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9C,kBAAkB,CAAC,KAAK,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM6C,mBAAmB,GAAIC,WAAW,IAAK;IAC3C;IACA1D,QAAQ,CAACD,KAAK,CAAC4D,GAAG,CAACxB,IAAI,IAAKA,IAAI,CAACgB,QAAQ,KAAKO,WAAW,CAACP,QAAQ,GAAGO,WAAW,GAAGvB,IAAK,CAAC,CAAC;IAC1FqB,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;;EAEC;EACA,MAAMI,iBAAiB,GAAGrE,OAAO,CAAC,MAAM;IACtC,MAAMsE,KAAK,GAAG,IAAIC,GAAG,CAAC/D,KAAK,CAAC4D,GAAG,CAACxB,IAAI,IAAIA,IAAI,CAAC4B,iBAAiB,CAAC,CAAC;IAChE,OAAO,CAAC,IAAI,EAAE,GAAGC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC;EACrC,CAAC,EAAE,CAAC9D,KAAK,CAAC,CAAC;;EAEb;EACA,MAAMmE,sBAAsB,GAAG3E,OAAO,CAAC,MAAM;IAC3C;IACA,MAAM4E,QAAQ,GAAG5D,YAAY,KAAK,IAAI,GAClCR,KAAK,GACLA,KAAK,CAACwD,MAAM,CAACpB,IAAI,IAAIA,IAAI,CAAC4B,iBAAiB,KAAKxD,YAAY,CAAC;;IAEjE;IACA,MAAM6D,MAAM,GAAG,CAAC,GAAGD,QAAQ,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,MAAMC,KAAK,GAAGF,CAAC,CAACP,iBAAiB,IAAI,EAAE;MACvC,MAAMU,KAAK,GAAGF,CAAC,CAACR,iBAAiB,IAAI,EAAE;MACvC,MAAMW,KAAK,GAAGJ,CAAC,CAACnB,QAAQ,IAAI,EAAE;MAC9B,MAAMwB,KAAK,GAAGJ,CAAC,CAACpB,QAAQ,IAAI,EAAE;MAE9B,IAAIqB,KAAK,GAAGC,KAAK,EAAE,OAAO,CAAC,CAAC;MAC5B,IAAID,KAAK,GAAGC,KAAK,EAAE,OAAO,CAAC;MAC3B,IAAIC,KAAK,GAAGC,KAAK,EAAE,OAAO,CAAC,CAAC;MAC5B,IAAID,KAAK,GAAGC,KAAK,EAAE,OAAO,CAAC;MAC3B,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAItE,SAAS,KAAK,MAAM,EAAE;MACxB,OAAO+D,MAAM,CAACQ,OAAO,CAAC,CAAC;IACzB;IACA,OAAOR,MAAM;EACf,CAAC,EAAE,CAACrE,KAAK,EAAEM,SAAS,EAAEE,YAAY,CAAC,CAAC;EAEpC,IAAIN,OAAO,EAAE,oBAAON,OAAA;IAAAkF,QAAA,EAAG;EAAS;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACpC,IAAI9E,KAAK,EAAE,oBAAOR,OAAA;IAAAkF,QAAA,GAAG,gBAAI,EAAC1E,KAAK;EAAA;IAAA2E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EAEpC,oBACEtF,OAAA;IAAAkF,QAAA,gBACElF,OAAA;MAAKuF,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAR,QAAA,gBAC1ElF,OAAA;QAAAkF,QAAA,EAAI;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACftF,OAAA;QAAKuF,KAAK,EAAE;UAAEI,UAAU,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACjClF,OAAA;UAAO4F,OAAO,EAAC,aAAa;UAAAV,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9CtF,OAAA;UACE6F,EAAE,EAAC,aAAa;UAChBC,KAAK,EAAElF,YAAa;UACpBmF,QAAQ,EAAEC,CAAC,IAAInF,eAAe,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAZ,QAAA,EAE9CjB,iBAAiB,CAACD,GAAG,CAACkC,IAAI,iBACzBlG,OAAA;YAAmB8F,KAAK,EAAEI,IAAK;YAAAhB,QAAA,EAAEgB;UAAI,GAAxBA,IAAI;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA6B,CAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNtF,OAAA;MAAOmG,SAAS,EAAC,YAAY;MAAAjB,QAAA,gBAC3BlF,OAAA;QAAAkF,QAAA,eACElF,OAAA;UAAAkF,QAAA,gBACElF,OAAA;YAAAkF,QAAA,EAAI;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZtF,OAAA;YAAAkF,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbtF,OAAA;YAAAkF,QAAA,EAAI;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZtF,OAAA;YAAAkF,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbtF,OAAA;YAAAkF,QAAA,EAAI;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACbtF,OAAA;YAAIoG,OAAO,EAAE/D,UAAW;YAACkD,KAAK,EAAE;cAAEc,MAAM,EAAE;YAAU,CAAE;YAAAnB,QAAA,GAAC,2BAChD,EAACxE,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG;UAAA;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACLtF,OAAA;YAAAkF,QAAA,EAAI;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRtF,OAAA;QAAAkF,QAAA,EACGX,sBAAsB,CAACP,GAAG,CAACxB,IAAI,iBAC9BxC,OAAA;UAAAkF,QAAA,gBACElF,OAAA;YAAAkF,QAAA,EAAK1C,IAAI,CAACgB;UAAQ;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBtF,OAAA;YAAAkF,QAAA,EAAK1C,IAAI,CAAC8D;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBtF,OAAA;YAAAkF,QAAA,EAAK1C,IAAI,CAAC+D;UAAO;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBtF,OAAA;YAAAkF,QAAA,EAAK1C,IAAI,CAACgE;UAAM;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtBtF,OAAA;YAAAkF,QAAA,EAAK1C,IAAI,CAACiE;UAAK;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBtF,OAAA;YAAAkF,QAAA,EAAK1C,IAAI,CAAC4B;UAAiB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjCtF,OAAA;YAAAkF,QAAA,gBACElF,OAAA;cAAQoG,OAAO,EAAEA,CAAA,KAAM7D,UAAU,CAACC,IAAI,CAAE;cAAA0C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpDtF,OAAA;cAAQoG,OAAO,EAAEA,CAAA,KAAM3D,YAAY,CAACD,IAAI,CAAE;cAAA0C,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA,GAVE9C,IAAI,CAACqD,EAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWZ,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEPxE,eAAe,IAAIE,WAAW,iBAC7BhB,OAAA;MAAKmG,SAAS,EAAC,eAAe;MAAAjB,QAAA,eAC5BlF,OAAA;QAAKmG,SAAS,EAAC,eAAe;QAAAjB,QAAA,eAC5BlF,OAAA,CAACF,YAAY;UACX0C,IAAI,EAAExB,WAAY;UAClB0F,QAAQ,EAAE7C,gBAAiB;UAC3B8C,SAAS,EAAE7C,mBAAoB;UAC/B5D,KAAK,EAAEA,KAAM;UACb0G,OAAO,EAAE,IAAK,CAAC;UAAA;UACf3C,iBAAiB,EAAEA,iBAAiB,CAACL,MAAM,CAACsC,IAAI,IAAIA,IAAI,KAAK,IAAI;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnF,EAAA,CAhNQF,mBAAmB;AAAA4G,EAAA,GAAnB5G,mBAAmB;AAkN5B,eAAeA,mBAAmB;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}