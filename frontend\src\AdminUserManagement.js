
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { API_ENDPOINTS } from './config';
import EditUserForm from './EditUserForm'; // 引入EditUserForm组件
import './SystemAdminPage.css'; // 引入样式

function AdminUserManagement({ token }) {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortOrder, setSortOrder] = useState('asc'); // 'asc' or 'desc'
  const [selectedType, setSelectedType] = useState('全部'); // New state for filtering
  const [isEditModalOpen, setIsEditModalOpen] = useState(false); // 控制编辑模态框的显示
  const [editingUser, setEditingUser] = useState(null); // 存储正在编辑的用户
 // const [organizationTypes, setOrganizationTypes] = useState([]);

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }, [token]);

  const fetchAllUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(API_ENDPOINTS.ADMIN_GET_ALL_SUB_USER, {
        method: 'GET',
        headers: getAuthHeaders(),
      });
      if (!response.ok) {
        throw new Error('Failed to fetch users.');
      }
      const data = await response.json();
      setUsers(data);
      console.log(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [getAuthHeaders]);

   useEffect(() => {
      fetchAllUsers();
    }, [fetchAllUsers]);
    
  // Function to toggle sort order
  const handleSort = () => {
    setSortOrder(currentOrder => (currentOrder === 'asc' ? 'desc' : 'asc'));
  };

  const handleEdit = (user) => {
    setEditingUser(user);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (userToDelete) => {
    if (!userToDelete || !userToDelete.short_name) {
      alert('用户信息不完整，无法进行删除操作。');
      return;
    }

    const parts = userToDelete.short_name.split('-');
    if (parts.length >= 3) {
      const level = parseInt(parts[0], 10);
      const parentIdentifier = parts[2];
      const childPrefix = `${level + 1}-${parentIdentifier}`;

      const hasChildren = users.some(user => 
        user.short_name && user.short_name.startsWith(childPrefix)
      );

      if (hasChildren) {
        alert('该用户存在下一级用户，无法删除。');
        return;
      }
    }

    if (window.confirm(`确定要删除用户 “${userToDelete.username}” 吗？`)) {
      try {
        const response = await fetch(API_ENDPOINTS.ADMIN_DELETE_USER, {
          method: 'DELETE',
          headers: getAuthHeaders(),
          body: JSON.stringify({ username: userToDelete.username }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || '删除用户失败');
        }

        setUsers(users.filter(user => user.username !== userToDelete.username));
        alert('用户删除成功');
      } catch (err) {
        setError(err.message);
        alert(`删除失败: ${err.message}`);
      }
    }
  };

  const handleCloseModal = () => {
    setIsEditModalOpen(false);
    setEditingUser(null);
  };

  const handleUpdateSuccess = (updatedUser) => {
    // 更新用户列表中的用户信息
    setUsers(users.map(user => (user.username === updatedUser.username ? updatedUser : user)));
    handleCloseModal(); // 关闭模态框
  };

    // Get unique organization types for the filter dropdown
    const organizationTypes = useMemo(() => {
      const types = new Set(users.map(user => user.organization_type));
      return ['全部', ...Array.from(types)];
    }, [users]);

  // Memoize the filtered and sorted users
  const filteredAndSortedUsers = useMemo(() => {
    // 1. Filter users based on selectedType
    const filtered = selectedType === '全部'
      ? users
      : users.filter(user => user.organization_type === selectedType);

    // 2. Sort the filtered users
    const sorted = [...filtered].sort((a, b) => {
      const typeA = a.organization_type || '';
      const typeB = b.organization_type || '';
      const nameA = a.username || '';
      const nameB = b.username || '';

      if (typeA < typeB) return -1;
      if (typeA > typeB) return 1;
      if (nameA < nameB) return -1;
      if (nameA > nameB) return 1;
      return 0;
    });

    if (sortOrder === 'desc') {
      return sorted.reverse();
    }
    return sorted;
  }, [users, sortOrder, selectedType]);

  if (loading) return <p>正在加载用户...</p>;
  if (error) return <p>错误: {error}</p>;

  return (
    <div>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
        <h4>所有用户列表</h4>
        <div style={{ marginLeft: '20px' }}>
          <label htmlFor="type-filter">按用户类别筛选: </label>
          <select 
            id="type-filter"
            value={selectedType}
            onChange={e => setSelectedType(e.target.value)}
          >
            {organizationTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>
      <table className="user-table">
        <thead>
          <tr>
            <th>用户名</th>
            <th>单位地址</th>
            <th>联系人</th>
            <th>联系电话</th>
            <th>电子邮件</th>
            <th onClick={handleSort} style={{ cursor: 'pointer' }}>
              用户类别 {sortOrder === 'asc' ? '▲' : '▼'}
            </th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          {filteredAndSortedUsers.map(user => (
            <tr key={user.id}>
              <td>{user.username}</td>
              <td>{user.address}</td>
              <td>{user.contact}</td>
              <td>{user.mobile}</td>
              <td>{user.email}</td>
              <td>{user.organization_type}</td>
              <td>
                <button onClick={() => handleEdit(user)}>编辑</button>
                <button onClick={() => handleDelete(user)}>删除</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {isEditModalOpen && editingUser && (
        <div className="modal-overlay">
          <div className="modal-content">
            <EditUserForm
              user={editingUser}
              onCancel={handleCloseModal}
              onSuccess={handleUpdateSuccess}
              token={token}
              isAdmin={true} // 传递isAdmin属性
              organizationTypes={organizationTypes.filter(type => type !== '全部')}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminUserManagement;
